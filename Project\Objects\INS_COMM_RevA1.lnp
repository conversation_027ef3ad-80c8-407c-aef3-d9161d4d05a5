--cpu=Cortex-M4.fp
".\objects\gd32f4xx_adc.o"
".\objects\gd32f4xx_can.o"
".\objects\gd32f4xx_crc.o"
".\objects\gd32f4xx_ctc.o"
".\objects\gd32f4xx_dac.o"
".\objects\gd32f4xx_dbg.o"
".\objects\gd32f4xx_dci.o"
".\objects\gd32f4xx_dma.o"
".\objects\gd32f4xx_enet.o"
".\objects\gd32f4xx_exmc.o"
".\objects\gd32f4xx_exti.o"
".\objects\gd32f4xx_fmc.o"
".\objects\gd32f4xx_fwdgt.o"
".\objects\gd32f4xx_gpio.o"
".\objects\gd32f4xx_i2c.o"
".\objects\gd32f4xx_ipa.o"
".\objects\gd32f4xx_iref.o"
".\objects\gd32f4xx_misc.o"
".\objects\gd32f4xx_pmu.o"
".\objects\gd32f4xx_rcu.o"
".\objects\gd32f4xx_rtc.o"
".\objects\gd32f4xx_sdio.o"
".\objects\gd32f4xx_spi.o"
".\objects\gd32f4xx_syscfg.o"
".\objects\gd32f4xx_timer.o"
".\objects\gd32f4xx_tli.o"
".\objects\gd32f4xx_trng.o"
".\objects\gd32f4xx_usart.o"
".\objects\gd32f4xx_wwdgt.o"
".\objects\gd32f4xx_it.o"
".\objects\main.o"
".\objects\systick.o"
".\objects\can_data.o"
".\objects\gnss.o"
".\objects\imu_data.o"
".\objects\ins_data.o"
".\objects\ins_sys.o"
".\objects\tcpserver.o"
".\objects\time_unify.o"
".\objects\bmp2.o"
".\objects\bmp280.o"
".\objects\bsp_adc.o"
".\objects\bsp_can.o"
".\objects\bsp_exti.o"
".\objects\bsp_flash.o"
".\objects\bsp_fmc.o"
".\objects\bsp_fwdgt.o"
".\objects\bsp_gpio.o"
".\objects\bsp_i2c.o"
".\objects\bsp_rtc.o"
".\objects\bsp_sys.o"
".\objects\bsp_tim.o"
".\objects\bsp_uart.o"
".\objects\ch378_hal.o"
".\objects\ch395cmd.o"
".\objects\ch395spi.o"
".\objects\common.o"
".\objects\file_sys.o"
".\objects\logger.o"
".\objects\tcp_server.o"
".\objects\ch378_spi_hw.o"
".\objects\startup_gd32f450_470.o"
".\objects\system_gd32f4xx.o"
"..\Library\CMSIS\arm_cortexM4lf_math.lib"
".\objects\data_convert.o"
".\objects\segger_rtt.o"
".\objects\segger_rtt_printf.o"
--library_type=microlib --strict --scatter ".\Objects\INS_COMM_RevA1.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\Listings\INS_COMM_RevA1.map" -o .\Objects\INS_COMM_RevA1.axf