/***********************************************************************************
nav gnss module header
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|DengWei       |  2023-5-22          | First Creation             |
|-----------+---------------+-------------------|  
***********************************************************************************/
#ifndef __NAV_GNSS_H__
#define __NAV_GNSS_H__
#include "nav_type.h"
#include "algorithm.h"

void GNSS_init();
void Get_GNSS_Data(_NAV_Data_Full_t* NAV_Data_Full_p,CombineDataTypeDef* CombineData_p);
unsigned char is_gnss_update(_NAV_Data_Full_t* NAV_Data_Full_p);


#endif

