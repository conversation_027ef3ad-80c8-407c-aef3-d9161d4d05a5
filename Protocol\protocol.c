#ifndef  __GOL_PROTOCOL_C__
#define  __GOL_PROTOCOL_C__

#include "computerFrameParse.h"
#include "protocol.h"
#include "serial.h"
#include "uartadapter.h"
#include "SetParaBao.h"

#include "gnss.h"
#include "frame_analysis.h"
#include "nav.h"
#include "fpgad.h"
#include "nav_cli.h"
#include "gdtypedefine.h"

static uint8_t gnrmcSendBuff[200];
static uint16_t gnrmcSendLen = 0;
static uint8_t gnggaSendBuff[200];
static uint16_t gnggaSendLen = 0;

uint32_t NavDataSendedCnt = 0;
uint8_t Cs, CsCharBuf[2];

union InsGnssData
{
    char  InsGnssDataChar[148 * 2];
    float InsGnssDataFloat[37 * 2];
} ins_gnss_data;

//#pragma pack(1)
struct InsGipotData
{
    uint16_t 	GPSWeek;		//周 1980-1-6 到当前的周数（导航系统时间）
    double 		GPSTime;		//以本周日 00:00:00 到当前的秒数（导航系统时间）
    double		Heading;		//偏航角（0~359.99度）
    double		Pitch;			//俯仰角（-90~90度）
    double		Roll;			//横滚角（-180~180度）
    double		Lattitude;  	//纬度（-90~90度）
    double		Longitude;  	//经度（-180~180度）
    double		Altitude;  		//高度（单位：米）
    double		Ve;  			//东向速度（单位：米/秒）
    double		Vn;  			//北向速度（单位：米/秒）
    double		Vu;  			//天向速度（单位：米/秒）
    double		Baseline;  		//基线长度（单位：米）
    uint8_t		NSV;  			//卫星数
    uint8_t		NavStatus;		//>0 表示导航解算有效
    uint8_t		GnssStatus;   	//系统状态：0：初始；1：单点定位2：伪距差分；4：载波相位差分（浮点）5：载波相位差分（固定）

} ins_gipot_data, ins_gpfpd_data;


struct InsGrimuData
{
    uint16_t 	GPSWeek;		//周 1980-1-6 到当前的周数（导航系统时间）
    double 		GPSTime;		//以本周日 00:00:00 到当前的秒数（导航系统时间）
    double		GyroX; 			//陀螺仪 X 轴角速度（度/s）
    double		GyroY; 			//陀螺仪 Y 轴角速度（度/s）
    double		GyroZ; 			//陀螺仪 Z 轴角速度（度/s）
    double		AccelX;  		//加速度计 X 轴加速度（m/s 2 ）
    double		AccelY;  		//加速度计 Y 轴加速度（m/s 2 ）
    double		AccelZ;  		//加速度计 Z 轴加速度（m/s 2 ）
    double		Temp;  			//陀螺仪温度计（摄氏度）

} ins_grimu_data;


typedef  struct
{
    char		head1;	  		//帧头  0xAA
    char		head2;	  		//帧头  0x44
    char		head3;	  		//帧头  0x13
    uint8_t		frameLen;		//帧长  不包括帧头和 CRC 校验的帧长度
    uint16_t	frameId;  		//帧号
    uint16_t 	GPSWeek;		//周 1980-1-6 到当前的周数（导航系统时间）
    uint32_t 	GPSMilliseconds;//以本周日 00:00:00 到当前的毫秒数（导航系统时间）
} RawimuHeader;


struct InsRawimuData
{
    RawimuHeader	header;
    uint16_t 		GPSWeek;		//周 1980-1-6 到当前的周数（导航系统时间）
    double 			GPSTime;		//以本周日 00:00:00 到当前的秒数（导航系统时间）
    uint32_t  		IMUStatus; 		//状态	//0X00000001 X 陀螺状态 1：正常 0：异常
    //0X00000002 Y 陀螺状态 1：正常 0：异常
    //0X00000004 Z 陀螺状态 1：正常 0：异常
    //0X00000010 X 加表状态 1：正常 0：异常
    //0X00000020 Y 加表状态 1：正常 0：异常
    //0X00000040 Z 加表状态 1：正常 0：异常
    int32_t  		AccelZ; 		//加表Z轴
    int32_t  		AccelY; 		//加表Y轴
    int32_t  		AccelX; 		//加表X轴
    int32_t			GyroZ; 			//陀螺仪Z轴
    int32_t			GyroY; 			//陀螺仪Y轴
    int32_t			GyroX;			//陀螺仪X轴
    uint32_t		CRC32;			//CRC 校验  32-bitCRC 校验
} ins_rawimu_data;

//#pragma pack()

void protocol_fillGipotData(void* pnav, void* pheading)
{
    _NAV_Data_Out_t* nav = (_NAV_Data_Out_t*)pnav;
    GNSS_Heading_DataTypeDef* heading = (GNSS_Heading_DataTypeDef*)pheading;

    ins_gipot_data.GPSWeek = heading->header.gpsWn;
    ins_gipot_data.GPSTime = heading->header.gpsMs * 0.001;
    ins_gipot_data.Heading = nav->heading;
    ins_gipot_data.Pitch = nav->pitch;
    ins_gipot_data.Roll = nav->roll;
    ins_gipot_data.Lattitude = nav->latitude;
    ins_gipot_data.Longitude = nav->longitude;
    ins_gipot_data.Altitude = nav->altitude;
    ins_gipot_data.Ve = nav->ve;
    ins_gipot_data.Vn = nav->vn;
    ins_gipot_data.Vu = nav->vu;
    ins_gipot_data.Baseline = heading->baseLen;
    ins_gipot_data.NSV = nav->Sate_Num;
    ins_gipot_data.GnssStatus = nav->rtkStatus;
    ins_gipot_data.NavStatus = ins_gipot_data.GnssStatus;

    memcpy((void*)&ins_gpfpd_data.GPSWeek, (void*)&ins_gipot_data.GPSWeek, sizeof(struct InsGipotData));
}

void protocol_fillGrimuData(void* pnav, void* pheading)
{
    _NAV_Data_Out_t* nav = (_NAV_Data_Out_t*)pnav;
    GNSS_Heading_DataTypeDef* heading = (GNSS_Heading_DataTypeDef*)pheading;

    ins_grimu_data.GPSWeek = heading->header.gpsWn;
    ins_grimu_data.GPSTime = heading->header.gpsMs * 0.001;
    ins_grimu_data.GyroX   = nav->gyroX;
    ins_grimu_data.GyroY   = nav->gyroY;
    ins_grimu_data.GyroZ   = nav->gyroZ;
    ins_grimu_data.AccelX  = nav->accelX;
    ins_grimu_data.AccelY  = nav->accelY;
    ins_grimu_data.AccelZ  = nav->accelZ;
    ins_grimu_data.Temp    = imuParseData.sensorTemp;
}

#define	RAWIMU_FRAME_LEN	( sizeof(struct InsRawimuData) - sizeof(RawimuHeader) - sizeof(uint32_t) )
void protocol_fillRawimuData(void* pnav, void* pheading)
{
    _NAV_Data_Out_t* nav = (_NAV_Data_Out_t*)pnav;
    GNSS_Heading_DataTypeDef* heading = (GNSS_Heading_DataTypeDef*)pheading;

    ins_rawimu_data.header.head1 			= 0xAA;
    ins_rawimu_data.header.head2 			= 0x44;
    ins_rawimu_data.header.head3 			= 0x13;
    ins_rawimu_data.header.frameLen 		= RAWIMU_FRAME_LEN;
    ins_rawimu_data.header.frameId 			= 325;
    ins_rawimu_data.header.GPSWeek 			= heading->header.gpsWn;
    ins_rawimu_data.header.GPSMilliseconds 	= heading->header.gpsMs;

    ins_rawimu_data.GPSWeek 				= heading->header.gpsWn;
    ins_rawimu_data.GPSTime 				= heading->header.gpsMs * 0.001;
    ins_rawimu_data.IMUStatus   			= IMUStatus;
    ins_rawimu_data.AccelZ   				= nav->accelX;//imu_info.accelZ_l | (imu_info.accelZ_h << 8); //imuParseData.accelGrp[2];
    ins_rawimu_data.AccelY   				= nav->accelY;//imu_info.accelY_l | (imu_info.accelY_h << 8); //imuParseData.accelGrp[1];
    ins_rawimu_data.AccelX  				= nav->accelZ;//imu_info.accelX_l | (imu_info.accelX_h << 8); //imuParseData.accelGrp[0];
    ins_rawimu_data.GyroZ  					= nav->gyroX;//imu_info.gyroZ_l | (imu_info.gyroZ_h << 8); //imuParseData.gyroGrp[2];
    ins_rawimu_data.GyroY  					= nav->gyroY;//imu_info.gyroY_l | (imu_info.gyroY_h << 8); //imuParseData.gyroGrp[1];
    ins_rawimu_data.GyroX    				= nav->gyroZ;//imu_info.gyroX_l | (imu_info.gyroX_h << 8); //imuParseData.gyroGrp[0];
    ins_rawimu_data.CRC32 					= crc_block_data_calculate((uint32_t*)&ins_rawimu_data.AccelZ, 6);

}

void protocol_crc32_init(void)
{
    rcu_periph_clock_enable(RCU_CRC);

    /* reset the CRC data register and calculate the CRC of the value */
    crc_data_register_reset();

}

/*****************************************************************************************
Function:HexToAscii
Input:none
Output:ret
Use:fpgaSem
Modify:fpgaSem
Note:
******************************************************************************************/
void HexToAscii( void * DestBuf, void * SrcBuf, int SrcLen )
{
    if ( ( DestBuf == NULL ) || ( SrcBuf == NULL ) )
    {
        return;
    }

    unsigned char * Dest = (unsigned char *) DestBuf;
    unsigned char * Src = (unsigned char *) SrcBuf;
    int DestPos = SrcLen << 1;
    int SrcPos = SrcLen;

    while ( --SrcPos >= 0 )
    {
        unsigned char HiHalf = Src[SrcPos] >> 4;
        unsigned char LoHalf = Src[SrcPos] & 0x0F;
        Dest[--DestPos] = ( HiHalf <= 9 ) ? ( HiHalf + '0' ) : ( HiHalf + 55 );
        Dest[--DestPos] = ( LoHalf <= 9 ) ? ( LoHalf + '0' ) : ( LoHalf + 55 );
    }

}

void protocol_gnrmcDataGet(uint8_t* pData, uint16_t* dataLen)
{
    uint16_t len = *dataLen;
    memcpy(gnrmcSendBuff, pData, len);
    gnrmcSendLen = len;
}

void protocol_gnggaDataGet(uint8_t* pData, uint16_t* dataLen)
{
    uint16_t len = *dataLen;
    memcpy(gnggaSendBuff, pData, len);
    gnggaSendLen = len;
}

void protocol_report(uint8_t channel)
{
    uint8_t i, frameType, dataMode;

    dataMode = comm_read_dataMode();
    frameType = comm_read_currentFrameType(channel);

    switch (frameType)
    {
        case cfg_format_GIPOT:
            if(dataMode == INS_DATA_MODE_1 || dataMode == INS_DATA_MODE_6)
            {
                sprintf(ins_gnss_data.InsGnssDataChar, "$GIPOT,%d,%.3f,%.3f,%.3f,%.3f,%.7f,%.7f,%.2f,%.3f,%.3f,%.3f,%.3f,%d,%d,%d*%d\r\n", \
                        ins_gipot_data.GPSWeek, ins_gipot_data.GPSTime, ins_gipot_data.Heading, ins_gipot_data.Pitch, \
                        ins_gipot_data.Roll, ins_gipot_data.Lattitude, ins_gipot_data.Longitude, ins_gipot_data.Altitude, \
                        ins_gipot_data.Ve, ins_gipot_data.Vn, ins_gipot_data.Vu, ins_gipot_data.Baseline, ins_gipot_data.NSV, ins_gipot_data.NavStatus, ins_gipot_data.GnssStatus, 66);
            }
            else if(dataMode == INS_DATA_MODE_2 || dataMode == INS_DATA_MODE_7)
            {
                sprintf(ins_gnss_data.InsGnssDataChar, "$GPFPD,%d,%.3f,%.3f,%.3f,%.3f,%.7f,%.7f,%.2f,%.3f,%.3f,%.3f,%.3f,%d,%d,%d*%d\r\n", \
                        ins_gipot_data.GPSWeek, ins_gipot_data.GPSTime, ins_gipot_data.Heading, ins_gipot_data.Pitch, \
                        ins_gipot_data.Roll, ins_gipot_data.Lattitude, ins_gipot_data.Longitude, ins_gipot_data.Altitude, \
                        ins_gipot_data.Ve, ins_gipot_data.Vn, ins_gipot_data.Vu, ins_gipot_data.Baseline, ins_gipot_data.NSV, ins_gipot_data.NavStatus, ins_gipot_data.GnssStatus, 66);
            }
            else if(dataMode == INS_DATA_MODE_3)
            {
                sprintf(ins_gnss_data.InsGnssDataChar, "$GRIMU,%d,%.3f,%.6f,%.6f,%.6f,%.7f,%.7f,%.7f,%.1f*%d\r\n", ins_grimu_data.GPSWeek, ins_grimu_data.GPSTime, ins_grimu_data.GyroX, ins_grimu_data.GyroY, ins_grimu_data.GyroZ, ins_grimu_data.AccelX, ins_grimu_data.AccelY, ins_grimu_data.AccelZ, ins_grimu_data.Temp, 66);
            }
            else if(dataMode == INS_DATA_MODE_5 || dataMode == INS_DATA_MODE_9)
            {
                sprintf(ins_gnss_data.InsGnssDataChar, "$GRIMU,%d,%.3f,%.6f,%.6f,%.6f,%.7f,%.7f,%.7f,%.1f\n$GPFPD,%d,%.3f,%.3f,%.3f,%.3f,%.7f,%.7f,%.2f,%.3f,%.3f,%.3f,%.3f,%d,%d,%d*%d\r\n",
                        ins_grimu_data.GPSWeek, ins_grimu_data.GPSTime, ins_grimu_data.GyroX, ins_grimu_data.GyroY, ins_grimu_data.GyroZ, ins_grimu_data.AccelX, ins_grimu_data.AccelY, ins_grimu_data.AccelZ, ins_grimu_data.Temp, \
                        ins_gipot_data.GPSWeek, ins_gipot_data.GPSTime, ins_gipot_data.Heading, ins_gipot_data.Pitch, ins_gipot_data.Roll, ins_gipot_data.Lattitude, ins_gipot_data.Longitude, ins_gipot_data.Altitude, \
                        ins_gipot_data.Ve, ins_gipot_data.Vn, ins_gipot_data.Vu, ins_gipot_data.Baseline, ins_gipot_data.NSV, ins_gipot_data.NavStatus, ins_gipot_data.GnssStatus, 66);
            }
            else if(dataMode == INS_DATA_MODE_4 || dataMode == INS_DATA_MODE_8)
            {
                if(NavDataSendedCnt % 2 == 1)
                {
                    sprintf(ins_gnss_data.InsGnssDataChar, "$GIPOT,%d,%.3f,%.3f,%.3f,%.3f,%.7f,%.7f,%.2f,%.3f,%.3f,%.3f,%.3f,%d,%d,%d*%d\r\n", \
                            ins_gipot_data.GPSWeek, ins_gipot_data.GPSTime, ins_gipot_data.Heading, ins_gipot_data.Pitch, \
                            ins_gipot_data.Roll, ins_gipot_data.Lattitude, ins_gipot_data.Longitude, ins_gipot_data.Altitude, \
                            ins_gipot_data.Ve, ins_gipot_data.Vn, ins_gipot_data.Vu, ins_gipot_data.Baseline, ins_gipot_data.NSV, ins_gipot_data.NavStatus, ins_gipot_data.GnssStatus, 66);
                }
                else
                {
                    sprintf(ins_gnss_data.InsGnssDataChar, "$GRIMU,%d,%.3f,%.6f,%.6f,%.6f,%.7f,%.7f,%.7f,%.1f*%d\r\n", ins_grimu_data.GPSWeek, ins_grimu_data.GPSTime, ins_grimu_data.GyroX, ins_grimu_data.GyroY, ins_grimu_data.GyroZ, ins_grimu_data.AccelX, ins_grimu_data.AccelY, ins_grimu_data.AccelZ, ins_grimu_data.Temp, 66);
                }
            }

            Cs = 0;

            for(i = 1; ins_gnss_data.InsGnssDataChar[i] != '*'; i++)
            {
                Cs ^= ins_gnss_data.InsGnssDataChar[i];
            }

            //计算校验和

            HexToAscii(CsCharBuf, &Cs, 1 );
            ins_gnss_data.InsGnssDataChar[i + 1] = CsCharBuf[1];
            ins_gnss_data.InsGnssDataChar[i + 2] = CsCharBuf[0];
            break;

        case cfg_format_GPRMC:
            memset((void*)ins_gnss_data.InsGnssDataChar, '\0', sizeof(ins_gnss_data.InsGnssDataChar));
            memcpy((void*)ins_gnss_data.InsGnssDataChar, (void*)gnrmcSendBuff, gnrmcSendLen);
            break;

        case cfg_format_GPGGA:
            memset((void*)ins_gnss_data.InsGnssDataChar, '\0', sizeof(ins_gnss_data.InsGnssDataChar));
            memcpy((void*)ins_gnss_data.InsGnssDataChar, (void*)gnggaSendBuff, gnggaSendLen);
            break;

        case cfg_format_RAWIMU:
            memset((void*)ins_gnss_data.InsGnssDataChar, '\0', sizeof(ins_gnss_data.InsGnssDataChar));
            memcpy((void*)ins_gnss_data.InsGnssDataChar, (void*)&ins_rawimu_data.header.head1, sizeof(struct InsRawimuData));
            break;

        default:
            break;
    }

    if(index_RS422 == channel)
    {
        Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, strlen(ins_gnss_data.InsGnssDataChar), (uint8_t*)ins_gnss_data.InsGnssDataChar);
    }
    else if(index_RS232A == channel)
    {

    }
    else if(index_RS232B == channel)
    {

    }
}

//航海通信协议---------------------start
enum
{
    e_iPMV_NAVI,			//开启/关闭指定端口 输出 模块 导系统 数据
    e_iPMV_IMU,				//开启/关闭指定端口 输出 IMU 原始数据 数据
    e_iPMV_SOLDEVI,			//开启/关闭指定端口输出解算标准数据
    e_iPMV_MODULESTA,		//开启/关闭 指定端口 输出 模块状态数据
    e_iPMV_PORTCFG,			//配置 指定端口 输出 参数
    e_iPMV_PORT_DISABLE,	//关闭 指定端口输出 数据
    e_iPMV_PORT_INQUIRE,	//查询 端口 输出 数据
    e_iPMV_MODULE_REG,		//模块 注册 命令
    e_iPMV_VER_INQUIRE,		//查询模块 版本
    e_iPMV_ARM_PARAM,		//设置/查询 模块 的 杆臂 参数 参数
    e_iPMV_ANGLE_DEVI,		//设置/查询 模块 安装 安装偏差


    e_iPMV_MAX				//关闭 指定端口输出 数据
};

static enum
{
    e_iPMV_ENABLE, e_iPMV_DISABLE = !e_iPMV_ENABLE
} iPMV_en __attribute__((unused)) = e_iPMV_DISABLE;

static enum
{
    e_iPMV_MODULE_STA_MSG, e_iPMV_MODULE_STA_BIN = !e_iPMV_MODULE_STA_MSG
} iPMV_MODULE_sta = e_iPMV_MODULE_STA_MSG;

#pragma pack(1)

typedef struct
{
	uint8_t 	header[3];
	uint32_t 	gpsweek;
	double	 	gpssecond;
	long		latitude;
	long		longitude;
	long		altitude;
	short		ve;
    short 		vn;
    short		vu;
    short 		roll;		//横滚角
    short 		pitch;		//俯仰角
    short		azimuth;	//方位角
    uint8_t		starNum;
    uint8_t		GINSPosStatus;
    uint8_t		AlignStatus;
    uint8_t		SrcFusion;
    uint8_t		GINSHeadingStatus;
    uint8_t		GINSVelStatus;
    uint16_t	age;
    uint8_t		xor;
    uint8_t		end;
}GINS_BIN_Typedef;
GINS_BIN_Typedef gins_bin_data;

typedef struct
{
    uint8_t 	header[3];
	uint8_t		status;
	uint32_t 	gpsweek;
	uint32_t	gpssecond;
    short 		accelX;		//加表x轴
    short 		accelY;		//加表y轴
    short		accelZ;		//加表z轴
    short 		gyroX;		//陀螺x轴
    short 		gyroY;		//陀螺y轴
	short		gyroZ;		//陀螺z轴
	short		temp;		//温度
    uint8_t		xor;
    uint8_t		end;
}GIMU_BIN_Typedef;
GIMU_BIN_Typedef gimu_bin_data;

typedef struct
{
    uint8_t 	header[3];
	uint32_t 	gpsweek;
	uint32_t	gpssecond;
    uint32_t 	latStd;
    uint32_t 	lonStd;
    uint32_t	altStd;
    uint32_t 	velEStd;
    uint32_t 	velNStd;
	uint32_t	velDStd;
	uint32_t	pitchStd;
	uint32_t	rollStd;
	uint32_t	headingStd;
    uint8_t		xor;
    uint8_t		end;
}GINSSTD_BIN_Typedef;
GINSSTD_BIN_Typedef ginsstd_bin_data;

#pragma pack()

static double iPMV_rate __attribute__((unused));
static uint8_t iPMV_type __attribute__((unused)) = 0;

void iPMV_GINS_BIN_Send(void* pnav, void *gps)
{
	uint8_t status = 0;
	_NAV_Data_Out_t* result = (_NAV_Data_Out_t*)pnav;
    GPSDataTypeDef* gnss = (GPSDataTypeDef*)gps;

	gins_bin_data.header[0] = 0xac;
    gins_bin_data.header[1] = 0xca;
    gins_bin_data.header[2] = 0x0c;

	gins_bin_data.gpsweek = gnss->gpsweek;
	gins_bin_data.gpssecond = gnss->gpssecond / 1000.0;
	gins_bin_data.latitude = (long)(gnss->Lat *10000000);
	gins_bin_data.longitude = (long)(gnss->Lon *10000000);
	gins_bin_data.altitude = (long)(gnss->Altitude *1000);
	gins_bin_data.ve = (long)(gnss->Lat *10000000);
	gins_bin_data.ve = (short)(result->ve / 100.0 * 32768);
    gins_bin_data.vn = (short)(result->vn / 100.0 * 32768);
    gins_bin_data.vu = (short)(result->vu / 100.0 * 32768);
    gins_bin_data.pitch = (short)(result->pitch / 360 * 32768);
    gins_bin_data.roll = (short)(result->roll / 360 * 32768);
    gins_bin_data.azimuth = (short)(result->heading / 360 * 32768);
    gins_bin_data.starNum = gnss->StarNum;
    gins_bin_data.GINSPosStatus = gnss->rtkStatus;
    if(combineData.Adj.Nav_Standard_flag == 2)
    	gins_bin_data.AlignStatus = 0x0f;
    else
    	gins_bin_data.AlignStatus = 0x0;
    if(combineData.canInfo.flag)
    	gins_bin_data.SrcFusion = 0x0f;
    else
    	gins_bin_data.SrcFusion = 0x0b;
    if(gnss->PositionType[0] == 52)status = 1;
    else if(gnss->PositionType[0] == 32)status = 2;
    else if(gnss->PositionType[0] == 17)status = 3;
    else if((gnss->PositionType[0] == 49) || (gnss->PositionType[0] == 50))status = 4;
    else if(gnss->PositionType[0] == 16)status = 5;
    gins_bin_data.GINSHeadingStatus = status;
    status = 0;
    if(gnss->PositionType[1] == 52)status = 1;
    else if(gnss->PositionType[1] == 32)status = 2;
    else if(gnss->PositionType[1] == 17)status = 3;
    else if((gnss->PositionType[1] == 49) || (gnss->PositionType[1] == 50))status = 4;
    else if(gnss->PositionType[1] == 16)status = 5;
    gins_bin_data.GINSVelStatus = status;
    gins_bin_data.age = (uint16_t)(gnss->Age*100);
    gins_bin_data.xor = xor_check((uint8_t*)&gins_bin_data.gpsweek, sizeof(gins_bin_data) - 5 );
    gins_bin_data.end = 0xbf;

    Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, sizeof(gins_bin_data), (uint8_t*)gins_bin_data.header);
}

void iPMV_GIMU_BIN_Send(void* pnav, void *gps)
{
//	uint8_t status = 0;
	_NAV_Data_Out_t* result = (_NAV_Data_Out_t*)pnav;
    GPSDataTypeDef* gnss = (GPSDataTypeDef*)gps;

	gimu_bin_data.header[0] = 0x7e;
    gimu_bin_data.header[1] = 0x7e;
    gimu_bin_data.header[2] = 0x0c;

	gimu_bin_data.status = 0x80;
	gimu_bin_data.gpsweek = gnss->gpsweek;
	gimu_bin_data.gpssecond = gnss->gpssecond;
	gimu_bin_data.accelX = (short)(result->accelX / 78.4 * 32768);
	gimu_bin_data.accelY = (short)(result->accelY / 78.4 * 32768);
	gimu_bin_data.accelZ = (short)(result->accelZ / 78.4 * 32768);
	gimu_bin_data.gyroX = (short)(result->gyroX / 500 * 32768);
	gimu_bin_data.gyroY = (short)(result->gyroY / 500 * 32768);
	gimu_bin_data.gyroZ = (short)(result->gyroZ / 500 * 32768);
    gimu_bin_data.temp = (short)(combineData.imuInfo.sensorTemp / 200 * 32768);

    gimu_bin_data.xor = xor_check((uint8_t*)&gimu_bin_data.status, sizeof(gimu_bin_data) - 5 );
    gimu_bin_data.end = 0xbf;

    Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, sizeof(gimu_bin_data), (uint8_t*)gimu_bin_data.header);
}

void iPMV_GINSSTD_BIN_Send(void* pnav, void *gps)
{
//	uint8_t status = 0;
	_NAV_Data_Out_t* result = (_NAV_Data_Out_t*)pnav;
    GPSDataTypeDef* gnss = (GPSDataTypeDef*)gps;

	ginsstd_bin_data.header[0] = 0xef;
    ginsstd_bin_data.header[1] = 0xfe;
    ginsstd_bin_data.header[2] = 0x0e;

	ginsstd_bin_data.gpsweek = gnss->gpsweek;
	ginsstd_bin_data.gpssecond = gnss->gpssecond;
	ginsstd_bin_data.latStd = (uint32_t)(gnss->LatStd * 1000);
	ginsstd_bin_data.lonStd = (uint32_t)(gnss->LonStd * 1000);
	ginsstd_bin_data.altStd = (uint32_t)(gnss->AltitudeStd * 1000);
	ginsstd_bin_data.velEStd = (uint32_t)(gnss->vestd * 1000);
	ginsstd_bin_data.velNStd = (uint32_t)(gnss->vnstd * 1000);
	ginsstd_bin_data.velDStd = (uint32_t)(gnss->vustd * 1000);
    ginsstd_bin_data.pitchStd = (uint32_t)(gnss->ptchstddev * 1000);
	ginsstd_bin_data.rollStd = 0;
	ginsstd_bin_data.headingStd = (uint32_t)(gnss->hdgstddev * 1000);

    ginsstd_bin_data.xor = xor_check((uint8_t*)&ginsstd_bin_data.gpsweek, sizeof(ginsstd_bin_data) - 5 );
    ginsstd_bin_data.end = 0xbf;

    Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, sizeof(ginsstd_bin_data), (uint8_t*)ginsstd_bin_data.header);
}

int iPMV_dataParse(char* pData, uint8_t index)
{

    switch(index)
    {
        case 0:			// 字段0
            if(NULL != strstr(pData, "$HSET"))
            {

            }
			else if(NULL != strstr(pData, "$HLOG"))
            {

            }
            break;

        case 1:			// 字段1
            if(0 == strncmp("GINSSTD", (char const *)pData, strlen("GINSSTD")))
            {
                iPMV_type = e_iPMV_SOLDEVI;
            }
            else if(0 == strncmp("IMU", (char const *)pData, strlen("IMU")))
            {
                iPMV_type = e_iPMV_IMU;
            }
            else if(0 == strncmp("GINS", (char const *)pData, strlen("GINS")))
            {
                iPMV_type = e_iPMV_NAVI;
            }
            else if(0 == strncmp("STA", (char const *)pData, strlen("STA")))
            {
                iPMV_type = e_iPMV_MODULESTA;
            }
			else if(0 == strncmp("SAVE", (char const *)pData, strlen("SAVE")))
            {
                iPMV_type = e_iPMV_PORTCFG;
            }
            else if(0 == strncmp("COM", (char const *)pData, strlen("COM")))
            {
                iPMV_type = e_iPMV_PORT_INQUIRE;
                iPMV_en = e_iPMV_ENABLE;
            }
            break;

        case 2:			// 字段2
            if(NULL != strstr(pData, "MSG"))
            {
                iPMV_MODULE_sta = e_iPMV_MODULE_STA_MSG;
            }
            else if(NULL != strstr(pData, "BIN"))
            {
                iPMV_MODULE_sta = e_iPMV_MODULE_STA_BIN;
            }

            break;

        case 3:			// 字段3
            if(NULL != strstr(pData, "A"))
            {

            }

            break;

        case 4:			// 字段4
            if(NULL != strstr(pData, "ON"))
            {
                iPMV_en = e_iPMV_ENABLE;
            }
            else if(NULL != strstr(pData, "OFF"))
            {
                iPMV_en = e_iPMV_DISABLE;
            }

            break;

        case 5:			// 字段5
            iPMV_rate = strtod(pData, NULL);
            break;

        default:
            break;
    }

    if(index == 5)
    {

        return 1;
    }
    else
        return 0;
}

void iPMV_protocolParse(uint8_t* pData, uint16_t dataLen)
{
    uint8_t index = 0;
    char * pBuf = NULL;
    char * pRxBuf = (char*)pData;
    char gnssCommaSeperator[] = ",";

    pBuf = strtok(pRxBuf, gnssCommaSeperator);

    while(NULL != pBuf)
    {
        iPMV_dataParse(pBuf, index);
        pBuf = strtok(NULL, gnssCommaSeperator);
        index++;
    }
}

void iPMV_protocol_report(void* pnav, void *gps)
#if 0
{
    static uint8_t iPMV_sendCnt = 0;
    static uint32_t timeSlice = 0;
    uint8_t i, status = 0, warning = 0;
    uint8_t freq = (uint8_t)(200 * iPMV_rate);
    _NAV_Data_Out_t* result = (_NAV_Data_Out_t*)pnav;
    GPSDataTypeDef* gnss = (GPSDataTypeDef*)gps;

//    if(iPMV_en == e_iPMV_DISABLE)return;

    memset((void*)ins_gnss_data.InsGnssDataChar, '\0', sizeof(ins_gnss_data.InsGnssDataChar));
    iPMV_sendCnt++;

    if(iPMV_sendCnt >= freq)
    {
        iPMV_sendCnt = 0;

        switch (iPMV_type)
        {
            case e_iPMV_NAVI:
                if(gnss->ResolveState[0] == 0)
                    status |= 0x1;
                else
                    status &= ~0x1;

                if(gnss->ResolveState[1] == 0)
                    status |= 0x2;
                else
                    status &= ~0x2;

                if(gnss->ResolveState[2] == 0)
                    status |= 0x4;
                else
                    status &= ~0x4;

                sprintf(ins_gnss_data.InsGnssDataChar, "$GINS,%d,%.3f,%.7f,%.7f,%.2f,%.3f,%.3f,%.3f,%.2f,%.2f,%.2f,%d,%d,%d,%d,%d,%d,%.2f,%d,*%d\r\n", \
                        gnss->gpsweek, gnss->gpssecond * 0.001, result->latitude, result->longitude, result->altitude, \
                        result->ve, result->vn, result->vu, result->pitch, result->roll, result->heading, result->Sate_Num, \
                        result->rtkStatus, gnss->PositionType[2], gnss->PositionType[1], status, 0x0f, gnss->Age, warning, 66);

                Cs = 0;

                for(i = 1; ins_gnss_data.InsGnssDataChar[i] != '*'; i++)
                {
                    Cs ^= ins_gnss_data.InsGnssDataChar[i];
                }

                //计算校验和

                HexToAscii(CsCharBuf, &Cs, 1 );
                ins_gnss_data.InsGnssDataChar[i + 1] = CsCharBuf[1];
                ins_gnss_data.InsGnssDataChar[i + 2] = CsCharBuf[0];
                break;

            case e_iPMV_IMU:
                sprintf(ins_gnss_data.InsGnssDataChar, "$GRIMU,%d,%.3f,%d,%.7f,%.7f,%.7f,%.7f,%.7f,%.7f,%.2f,*%d\r\n", \
                        gnss->gpsweek, gnss->gpssecond * 0.001, status, result->accelX, result->accelY, result->accelZ, \
                        result->gyroX, result->gyroY, result->gyroZ, combineData.imuInfo.sensorTemp, 66);

                Cs = 0;

                for(i = 1; ins_gnss_data.InsGnssDataChar[i] != '*'; i++)
                {
                    Cs ^= ins_gnss_data.InsGnssDataChar[i];
                }

                //计算校验和

                HexToAscii(CsCharBuf, &Cs, 1 );
                ins_gnss_data.InsGnssDataChar[i + 1] = CsCharBuf[1];
                ins_gnss_data.InsGnssDataChar[i + 2] = CsCharBuf[0];
                break;

            case e_iPMV_SOLDEVI:
                sprintf(ins_gnss_data.InsGnssDataChar, "$GINSSTD,%d,%.3f,%.4f,%.4f,%.4f,%.4f,%.4f,%.4f,%.4f,%.4f,%.4f,*%d\r\n", \
                        gnss->gpsweek, gnss->gpssecond * 0.001, gnss->LatStd, gnss->LonStd, gnss->AltitudeStd, \
                        gnss->vestd, gnss->vnstd, gnss->vustd, gnss->ptchstddev, 0.0, gnss->hdgstddev, 66);

                Cs = 0;

                for(i = 1; ins_gnss_data.InsGnssDataChar[i] != '*'; i++)
                {
                    Cs ^= ins_gnss_data.InsGnssDataChar[i];
                }

                //计算校验和

                HexToAscii(CsCharBuf, &Cs, 1 );
                ins_gnss_data.InsGnssDataChar[i + 1] = CsCharBuf[1];
                ins_gnss_data.InsGnssDataChar[i + 2] = CsCharBuf[0];
                break;

            case e_iPMV_MODULESTA:
            	frame_iPMV_pack_and_send(&NAV_Data_Out, &hGPSData);
                if(iPMV_MODULE_sta == e_iPMV_MODULE_STA_MSG)
                {
                    sprintf(ins_gnss_data.InsGnssDataChar, "$GSTA,%d,%d,%s,%d,%d,%d,%d,*%d\r\n", \
                            moduleStatus.expiredStatus, moduleStatus.calibStatus, "GIDO", moduleStatus.gnssStatus, \
                            moduleStatus.imuStatus, moduleStatus.diffStatus, moduleStatus.odoStatus, 66);

                    Cs = 0;

                    for(i = 1; ins_gnss_data.InsGnssDataChar[i] != '*'; i++)
                    {
                        Cs ^= ins_gnss_data.InsGnssDataChar[i];
                    }

                    //计算校验和

                    HexToAscii(CsCharBuf, &Cs, 1 );
                    ins_gnss_data.InsGnssDataChar[i + 1] = CsCharBuf[1];
                    ins_gnss_data.InsGnssDataChar[i + 2] = CsCharBuf[0];
                }
                else
                {
                    Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, sizeof(moduleStatus), moduleStatus.header);
                    return;
                }

                break;

			case e_iPMV_PORT_INQUIRE:
				iPMV_type = e_iPMV_MAX;
				iPMV_en = e_iPMV_DISABLE;
				Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, strlen("COM Enabled Message:\r\nASCII:\r\n        GINS[1]\r\n        IMU[1]\r\n        STATUS[1]\r\n"), (uint8_t*)"COM Enabled Message:\r\nASCII:\r\n        GINS[1]\r\n        IMU[1]\r\n        STATUS[1]\r\n");
				Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, strlen("------\r\nBIN:\r\n<LOG OK>\r\n"), (uint8_t*)"------\r\nBIN:\r\n<LOG OK>\r\n");
				break;
            default:
                break;
        }
    }
	timeSlice++;
	if( timeSlice % 12000 == 0 )
	{
		iPMV_type++;
		if(iPMV_type == e_iPMV_PORTCFG)
		{
			iPMV_type = e_iPMV_NAVI;
			timeSlice = 0;
		}
		if((iPMV_type == e_iPMV_SOLDEVI) || (iPMV_type == e_iPMV_MODULESTA))
		{
			iPMV_rate = 1.0;
		}
		else
		{
			iPMV_rate = 0.01;
		}
	}
    Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, strlen(ins_gnss_data.InsGnssDataChar), (uint8_t*)ins_gnss_data.InsGnssDataChar);

}
#else
{
    static uint8_t iPMV_sendCnt = 0;
    static uint8_t iPMV_count = 0;
    _NAV_Data_Out_t* result = (_NAV_Data_Out_t*)pnav;
    GPSDataTypeDef* gnss = (GPSDataTypeDef*)gps;

	if(0 != combineData.outputType)return;
    iPMV_sendCnt++;
    if(iPMV_sendCnt >= 2)
    {
        iPMV_sendCnt = 0;
        iPMV_GINS_BIN_Send(pnav, gps);
        iPMV_GIMU_BIN_Send(pnav, gps);
        iPMV_count++;
        if(iPMV_count >= 100)
        {
        	iPMV_count = 0;
        	iPMV_GINSSTD_BIN_Send(pnav, gps);
        }
    }


}
#endif
//航海通信协议---------------------end

#endif




//only one ARM project...
extern unsigned char gframeParsebuf[FRAMEPARSEBUFSIZE];  // 在fpgad.c中定义
unsigned char gframeParsebufTxt[FRAMEPARSEBUFSIZE];
void analysisRxdata(void)
{
	//#define	U4RX_MAXCOUNT	(1024 * 4)
	//extern	unsigned char grxbuffer[U4RX_MAXCOUNT];
	//extern	int grxlen, grxst;
	//frameParse(grxbuffer, grxst);
	//uint8_t  frameHead[3] = {0xAF, 0x55, 0xFA};
	int i, j, bfind2 = 0, bfindT = 0;
	int size, type, size0, size1, ret, bsend = 0;
	unsigned char checksum, checksumcal, tail;
	int startbk = grxst, rxlenbx = grxlen, isbkexit = 0;
	for (i = 0; i < grxlen; i++) {
		if (grxbuffer[(grxst + i) % U4RX_MAXCOUNT] == 0xAF && grxbuffer[(grxst + i + 1) % U4RX_MAXCOUNT] == 0x55 && grxbuffer[(grxst + i + 2) % U4RX_MAXCOUNT] == 0xFA) {
      goutputmode = 0x10;
			gframeParsebuf[0] = 0xaf;
			gframeParsebuf[1] = 0x55;
			gframeParsebuf[2] = 0xfa;
			for (j = i + 3; j < grxlen; j++) {
				gframeParsebuf[j - i] = grxbuffer[(grxst + j) % U4RX_MAXCOUNT];
				if (grxbuffer[(grxst + j) % U4RX_MAXCOUNT] == 0xAF && grxbuffer[(grxst + j + 1) % U4RX_MAXCOUNT] == 0x55 && grxbuffer[(grxst + j + 2) % U4RX_MAXCOUNT] == 0xFA) {
					bfind2 = 1;
					break;
				}
				//if (j == grxlen - 1 && grxbuffer[(grxst + j) % U4RX_MAXCOUNT] == 0xff) //旧协议不包含 帧尾 0xFF
				if (j == SETPARA_RXBUFFER_DMA_SIZE - 1 && grxbuffer[(grxst + j-1) % U4RX_MAXCOUNT] == 0x00 && grxbuffer[(grxst + j) % U4RX_MAXCOUNT] == 0xff) //新协议 数据len=256，帧尾 0x00 0xFF
        {
					bfind2 = 1;
					j++;
					break;
				}
			}
			if (bfind2 == 0)	break;
			grxst = (grxst + j) % U4RX_MAXCOUNT;
			grxlen -= j;
			isbkexit = 0;
			break;
		}
		else if (grxbuffer[(grxst + i) % U4RX_MAXCOUNT] == 0xaa && grxbuffer[(grxst + i + 1) % U4RX_MAXCOUNT] == 0x55) {
			size = grxbuffer[(grxst + 2 + i) % U4RX_MAXCOUNT] + (grxbuffer[(grxst + 3 + i) % U4RX_MAXCOUNT] << 8);
			if (size < 6)	continue;
			if (size <= grxlen - i) {
				type = grxbuffer[(grxst + 4 +  i) % U4RX_MAXCOUNT];
				(void)type; // 避免未使用变量警告
				checksumcal = 0;
				for (j = 0; j < size - 2; j++) checksumcal += grxbuffer[(grxst + i + j) % U4RX_MAXCOUNT];
				checksum = grxbuffer[(grxst + i + size - 2)];
                tail = grxbuffer[(grxst + i + size - 1)];
				if (checksum == checksumcal) {
                    if (tail == 0xfe) {
                        txmsgserialport_t tmptxserialportmsg;
                        size0 = U4RX_MAXCOUNT - (grxst + i) % U4RX_MAXCOUNT;
						if (size0 >= size) {
							tmptxserialportmsg = *(txmsgserialport_t*)&(grxbuffer[(grxst + i) % U4RX_MAXCOUNT]);
						}
						else {
							size1 = size - size0;
							memcpy(&tmptxserialportmsg, &grxbuffer[(grxst + i) % U4RX_MAXCOUNT], size0);
							memcpy((unsigned char*)&tmptxserialportmsg + size0, &grxbuffer[0], size1);
						}
                        ret = mcusendtopcdriversdata(tmptxserialportmsg.cmd, tmptxserialportmsg.parameter0, tmptxserialportmsg.parameter1);
                        if (ret == 1)   bsend = 1;
                    }
				}
				else {	//checksum error!!!
                    if (tail == 0xfe) {
                        tail += 0;
                    }
				}
				i += size;
				rxlenbx = grxlen - i;
				startbk = (grxst + i) % U4RX_MAXCOUNT;
				i--;
				isbkexit = 1;
			}
		}
		//else if (grxbuffer[(grxst + i) % U4RX_MAXCOUNT] == '#' && grxbuffer[(grxst + i + 1) % U4RX_MAXCOUNT] == '^' && grxbuffer[(grxst + i + 2) % U4RX_MAXCOUNT] == '#') {
		else if (grxbuffer[(grxst + i) % U4RX_MAXCOUNT] == '$' && grxbuffer[(grxst + i + 1) % U4RX_MAXCOUNT] == 'A' && grxbuffer[(grxst + i + 2) % U4RX_MAXCOUNT] == 'L'
			&& grxbuffer[(grxst + i + 3) % U4RX_MAXCOUNT] == 'G' && grxbuffer[(grxst + i + 4) % U4RX_MAXCOUNT] == 'O') {
			gframeParsebufTxt[0] = '$';
			gframeParsebufTxt[1] = 'A';
			gframeParsebufTxt[2] = 'L';
			gframeParsebufTxt[3] = 'G';
			gframeParsebufTxt[4] = 'O';
			for (j = i + 5; j < grxlen; j++) {
				gframeParsebufTxt[j - i] = grxbuffer[(grxst + j) % U4RX_MAXCOUNT];
				if (grxbuffer[(grxst + j) % U4RX_MAXCOUNT] == '\r' && grxbuffer[(grxst + j + 1) % U4RX_MAXCOUNT] == '\n') {
					bfindT = 1;
					gframeParsebufTxt[j - i + 1] = grxbuffer[(grxst + j + 1) % U4RX_MAXCOUNT];
					break;
				}
			}
			if (bfindT == 0)	break;
			gframeParsebufTxt[j - i + 2] = '\0';
			grxst = (grxst + j) % U4RX_MAXCOUNT;
			grxlen -= j;
			isbkexit = 0;
			break;
		}
		else if (grxbuffer[(grxst + i) % U4RX_MAXCOUNT] == '#' && grxbuffer[(grxst + i + 1) % U4RX_MAXCOUNT] == 'S' && grxbuffer[(grxst + i + 2) % U4RX_MAXCOUNT] == 'E'
			&& grxbuffer[(grxst + i + 3) % U4RX_MAXCOUNT] == 'T' && grxbuffer[(grxst + i + 4) % U4RX_MAXCOUNT] == ':') {
			gframeParsebufTxt[0] = '#';
			gframeParsebufTxt[1] = 'S';
			gframeParsebufTxt[2] = 'E';
			gframeParsebufTxt[3] = 'T';
			gframeParsebufTxt[4] = ':';
			for (j = i + 5; j < grxlen; j++) {
				gframeParsebufTxt[j - i] = grxbuffer[(grxst + j) % U4RX_MAXCOUNT];
				if (grxbuffer[(grxst + j) % U4RX_MAXCOUNT] == '\r' && grxbuffer[(grxst + j + 1) % U4RX_MAXCOUNT] == '\n') {
					bfindT = 1;
					gframeParsebufTxt[j - i + 1] = grxbuffer[(grxst + j + 1) % U4RX_MAXCOUNT];
					break;
				}
			}
			if (bfindT == 0)	break;
			gframeParsebufTxt[j - i + 2] = '\0';
			grxst = (grxst + j) % U4RX_MAXCOUNT;
			grxlen -= j;
			break;
		}
		if (bsend)  break;
	}
	if (isbkexit) {
		grxst = startbk;
		grxlen = rxlenbx;
	}
	if (bfind2) {
		//frameParse(gframeParsebuf, 0);
    UartDmaRecSetPara((p_dmauart_t)gframeParsebuf);
	}
	if (bfindT) {
		bfindT = 0;
		ParseStrCmd((char*)gframeParsebufTxt);
	}
}

