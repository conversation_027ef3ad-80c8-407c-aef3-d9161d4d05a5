﻿#SINS配置文件
#*******************************device parameters******************************************
#IMU选择 0:mems 1:ifog
imuSelect=0
#0: imu460/dy数据仿真 1:scha63x 2:ADI16465 3:EPSON-G370
memsType=1
#********************************************************************************

#*******************************installation parameters******************************************
#载体坐标系下矢量，3
#gnssArmLength=0.6939,0.007,0.0984
gnssArmLength=0.5679,-0.08,0.0984
#双天线相对IMU的安装角度，粗略估计
#gnssAtt_from_vehicle=0,0,-92.3
gnssAtt_from_vehicle=0,0,270
#车辆转动引起的牵连运动，暂时未用到
OD_ArmLength=0,0,0
#车底盘相对IMU的安装角度，只是有俯仰和横滚，航向需要标定计算
#OD_Att_from_vehicle=-1.29,-0.02,0
#OD_Att_from_vehicle=-1.15,0.15,0
OD_Att_from_vehicle=0.0,0.0,0
#********************************************************************************

#*******************************Adjust parameters******************************************
#设备是否需要进行标定，0：需要标定；2：表示无需标定
Adj_Nav_Standard_flag=0
#陀螺初始零偏，rad/s，转台标定获取
#Adj_gyro_off=0.0,0.0,0.0003636
Adj_gyro_off=0.0,0.0,0.0
#加计初始零偏,单位m/s2，转台标定获取
#Adj_acc_off=0.012872,0.006316,0.017521
Adj_acc_off=0.0,0.0,0.0
#天线安装角精准误差，单位度
Adj_gnssheading_from_vehicle=0.0
#IMU与载体的y轴夹角
Adj_att_ods2_b_filte_2=0.0
#**********************************************************************************

#********************************Algorithm parameters*****************************************
#采用算法:0:全程kalman滤波；1：ODS模式采用航位推算
methodType=0
#仅仅航位推算，IMU补偿，度/h;目前#wb_set=0,0,8.15;隧道#wb_set=0,0,-6.0;地下车库#wb_set=0,0,80.0
wb_set=0,0,-68.0
#是否启动高精度计算,0:不启动；1：启动
HP=0
#********************************************************************************

#*******************************Deubg parameters******************************************
#仿真or正常运行,参数1为：0表示正常运行1表示仿真
simulate=1
#仿真失锁所在历元数204237，270000
lostepoch=160000
#********************************************************************************







