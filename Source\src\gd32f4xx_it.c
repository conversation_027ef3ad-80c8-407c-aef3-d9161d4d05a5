/*!
    \file  gd32f4xx_it.c
    \brief interrupt service routines
*/

/*
    Copyright (C) 2016 GigaDevice

    2016-08-15, V1.0.0, firmware for GD32F4xx
*/

#include "gd32f4xx_it.h"
#include "gd32f4xx.h"
#include "main.h"
#include "systick.h"
#include "bsp_fmc.h"
#include "INS_Data.h"
#include "bsp_tim.h"
#include "bsp_rtc.h"
#include "Logger.h"
#include "bsp_gpio.h"
#include "stdarg.h"
#include "fpgad.h"
#include "FirmwareUpdateFile.h"


extern uint8_t fisrtTimeGNSSTimeSync;			//首次时间同步信号
extern uint8_t fpga_syn;

extern int g_gpsWeek;							//GPS周数		周
extern double g_gpsSecond;						//GPS秒数		秒

extern char g_logFileName[];
extern uint8_t g_usb_ready;
extern LogBufTypeDef* g_pLogBuf;

extern uint8_t g_KF_OutData_Rx_Flag;

extern uint32_t g_CAN_Timeout_Start_flag;
extern uint32_t g_CAN_Timeout_Cnt;

/*!
    \brief      this function handles NMI exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void NMI_Handler(void)
{
}

/*!
    \brief      this function handles HardFault exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void HardFault_Handler(void)
{
    /* if Hard Fault exception occurs, go to infinite loop */
    while (1){
    }
}

/*!
    \brief      this function handles MemManage exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void MemManage_Handler(void)
{
    /* if Memory Manage exception occurs, go to infinite loop */
    while (1){
    }
}

/*!
    \brief      this function handles BusFault exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void BusFault_Handler(void)
{
    /* if Bus Fault exception occurs, go to infinite loop */
    while (1){
    }
}

/*!
    \brief      this function handles UsageFault exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void UsageFault_Handler(void)
{
    /* if Usage Fault exception occurs, go to infinite loop */
    while (1){
    }
}

/*!
    \brief      this function handles SVC exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
//void SVC_Handler(void)
//{
//}

/*!
    \brief      this function handles DebugMon exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void DebugMon_Handler(void)
{
}

/*!
    \brief      this function handles PendSV exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
//void PendSV_Handler(void)
//{
//}

/*!
    \brief      this function handles SysTick exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void SysTick_Handler(void)
{
	delay_decrement();
}

/*!
    \brief      this function handles ADC
    \param[in]  none
    \param[out] none
    \retval     none
*/
void ADC_IRQHandler(void)
{
	/* clear the ADC interrupt or status flag */
	adc_interrupt_flag_clear(ADC2, ADC_INT_FLAG_WDE);

}

void EXTI5_9_IRQHandler(void)
{
	if(RESET != exti_interrupt_flag_get(EXTI_7)) {				//ARM 1 to ARM 2 sync INT
//		gtime_t gt;
//		rtc_parameter_struct rtc_arg;
//		struct tm time;
//		DRam_Read(0,(uint16_t*)&g_week,2);
//		DRam_Read(2,(uint16_t*)&g_second,4);
//		if(fisrtTimeGNSSTimeSync == 1)
//		{
//			fisrtTimeGNSSTimeSync = 0;
//			gt = gpst2time(g_week,g_second);
//			gt = timeadd(gt,0.0435);			//GNSS PPS ʱ��ͬ������
//			gt = gpst2utc(gt);					//GPSʱ��ת����UTCʱ��
//			memcpy(&time,localtime(&gt.time),sizeof(struct tm));
//			rtc_arg.day_of_week = time.tm_wday;
//			rtc_arg.display_format = RTC_24HOUR;
//			rtc_arg.year = time.tm_year/10 + time.tm_year%10;
//			rtc_arg.month = time.tm_mon/10 + time.tm_mon%10;
//			rtc_arg.date = time.tm_mday/10 + time.tm_mday%10;
//			rtc_arg.hour = time.tm_hour/10 + time.tm_hour%10;
//			rtc_arg.minute = time.tm_min/10 + time.tm_min%10;
//			rtc_arg.second = time.tm_sec/10 + time.tm_sec%10;
//
//			if(rtc_arg.hour > 12)
//				rtc_arg.am_pm = RTC_PM;
//			else
//				rtc_arg.am_pm = RTC_AM;
//			rtc_setup(&rtc_arg);
//		}
		memset(&hINSFPGAData.data_stream, 0, sizeof(hINSFPGAData));
		DRam_Read(0x400,(uint16_t*)&hINSFPGAData.fpga_cache,sizeof(hINSFPGAData.fpga_cache)/sizeof(uint16_t));
		g_LEDIndicatorState = hINSFPGAData.data_stream.reserved[0];
		if(g_usb_ready == 1)
		{
			synthesisLogBuf((uint8_t*)&hINSFPGAData.data_stream,sizeof(hINSFPGAData.fpga_cache)/sizeof(uint8_t),Log_Type_0,g_pLogBuf);
			writeCSVLog((unsigned char*)g_logFileName,g_pLogBuf);
		}
		exti_interrupt_flag_clear(EXTI_7);
	}
	else if(RESET != exti_interrupt_flag_get(EXTI_9)) {			//CAN1 ����  INT
		exti_interrupt_flag_clear(EXTI_9);
	}
}

void EXTI3_IRQHandler(void)
{
	if(RESET != exti_interrupt_flag_get(EXTI_3))					//FPGA PWM sync INT				912
	{
		if(RESET != exti_interrupt_flag_get(EXTI_3))					//FPGA PWM sync INT			622
		{
			fpga_syn = 1;
			exti_interrupt_flag_clear(EXTI_3);
		}
	}
}

/*!
    \brief      this function handles external lines 10 to 15 interrupt request
    \param[in]  none
    \param[out] none
    \retval     none
*/
void EXTI10_15_IRQHandler(void)
{
	if(RESET != exti_interrupt_flag_get(EXTI_11))					//FPGA PWM sync INT
	{
		fpga_syn = 1;
//		getRTCWeekSecond(&g_gpsWeek,&g_gpsSecond);
//		DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CanDataTypeDef));
//		if(fisrtTimeGNSSTimeSync == 0)
//		{
//			ARM2_OUTPUT_ARM1_Low();
//			ARM2_OUTPUT_ARM1_Low();
//			ARM2_OUTPUT_ARM1_High();								// ARM2 to ARM1 Sync INT
//		}
		exti_interrupt_flag_clear(EXTI_11);
	}
	else if(RESET != exti_interrupt_flag_get(EXTI_14))				//CAN2 ����  INT
	{
		exti_interrupt_flag_clear(EXTI_14);
	}
}

void TIMER0_UP_TIMER9_IRQHandler(void)
{
	if(SET == timer_interrupt_flag_get(TIMER0,TIMER_INT_FLAG_UP))
	{
		time_periodic_sec_cnt ++;
		if(time_periodic_sec_cnt == 1000)
		{
			time_periodic_sec_cnt = 0;
			time_periodic_min_cnt ++;
			if(time_periodic_min_cnt == 60)
			{
				time_periodic_min_cnt = 0;
				time_periodic_hour_cnt ++;
				memset(g_logFileName,0,sizeof(char)*256);
				generateCSVLogFileName(g_logFileName);
				if(time_periodic_hour_cnt == 4)
				{
					time_periodic_hour_cnt = 0;
					time_sync_flag = 1;
					timeSync(g_week,g_second);
				}
			}
		}
	}
}

void TIMER2_IRQHandler(void)
{
	time_base_periodic_cnt ++;
	time_base_100ms_periodic_cnt ++;
	time_base_20ms_periodic_cnt++;
	if(SET == timer_interrupt_flag_get(TIMER2, TIMER_INT_FLAG_UP))
	{
		timer_interrupt_flag_clear(TIMER2, TIMER_INT_FLAG_UP);
		if(time_base_periodic_cnt == 1000)
			time_base_periodic_cnt = 0;
		if(time_base_100ms_periodic_cnt == 100)
		{
			time_base_100ms_periodic_cnt = 0;
			time_base_100ms_Flag = 1;
		}
		if(time_base_20ms_periodic_cnt == 20)
		{
			time_base_20ms_periodic_cnt = 0;
			time_base_20ms_Flag = 1;
		}

		if(g_CAN_Timeout_Start_flag == 1)
			g_CAN_Timeout_Cnt ++;
		if(g_CAN_Timeout_Cnt >= 1000)
		{
			g_CAN_Timeout_Cnt = 0;
			g_LEDIndicatorState = g_LEDIndicatorState & 0x0D;
		}
	}
}

void TIMER1_IRQHandler(void)
{

}

#ifdef USE_ENET_INTERRUPT
/*!
    \brief      this function handles ethernet interrupt request
    \param[in]  none
    \param[out] none
    \retval     none
*/
//void ENET_IRQHandler(void)
//{
//    portBASE_TYPE xHigherPriorityTaskWoken = pdFALSE;

//    /* frame received */
//    if(SET == enet_interrupt_flag_get(ENET_DMA_INT_FLAG_RS)){
//        /* give the semaphore to wakeup LwIP task */
//        xSemaphoreGiveFromISR(g_rx_semaphore, &xHigherPriorityTaskWoken);
//    }

//    /* clear the enet DMA Rx interrupt pending bits */
//    enet_interrupt_flag_clear(ENET_DMA_INT_FLAG_RS_CLR);
//    enet_interrupt_flag_clear(ENET_DMA_INT_FLAG_NI_CLR);

//    /* switch tasks if necessary */
//    if(pdFALSE != xHigherPriorityTaskWoken){
//        portEND_SWITCHING_ISR(xHigherPriorityTaskWoken);
//    }
//
//}
#endif /* USE_ENET_INTERRUPT */





//#define BUFFER_SIZE   (COUNTOF(tx_buffer))
//#define COUNTOF(a)   (sizeof(a)/sizeof(*(a)))
#define BUFFER_SIZE_TX   (1024 * 2)
#define BUFFER_SIZE_RX   (1024 * 4)
uint8_t tx_buffer[BUFFER_SIZE_TX] = {0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F,
                       0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F,
                       0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C, 0x2D, 0x2E, 0x2F,
                       0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x3C, 0x3D, 0x3E, 0x3F,
                       0x40, 0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4A, 0x4B, 0x4C, 0x4D, 0x4E, 0x4F,
                       0x50, 0x51, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57, 0x58, 0x59, 0x5A, 0x5B, 0x5C, 0x5D, 0x5E, 0x5F,
                       0x60, 0x61, 0x62, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68, 0x69, 0x6A, 0x6B, 0x6C, 0x6D, 0x6E, 0x6F,
                       0x70, 0x71, 0x72, 0x73, 0x74, 0x75, 0x76, 0x77, 0x78, 0x79, 0x7A, 0x7B, 0x7C, 0x7D, 0x7E, 0x7F,
                       0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87, 0x88, 0x89, 0x8A, 0x8B, 0x8C, 0x8D, 0x8E, 0x8F,
                       0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97, 0x98, 0x99, 0x9A, 0x9B, 0x9C, 0x9D, 0x9E, 0x9F,
                       0xA0, 0xA1, 0xA2, 0xA3, 0xA4, 0xA5, 0xA6, 0xA7, 0xA8, 0xA9, 0xAA, 0xAB, 0xAC, 0xAD, 0xAE, 0xAF,
                       0xB0, 0xB1, 0xB2, 0xB3, 0xB4, 0xB5, 0xB6, 0xB7, 0xB8, 0xB9, 0xBA, 0xBB, 0xBC, 0xBD, 0xBE, 0xBF,
                       0xC0, 0xC1, 0xC2, 0xC3, 0xC4, 0xC5, 0xC6, 0xC7, 0xC8, 0xC9, 0xCA, 0xCB, 0xCC, 0xCD, 0xCE, 0xCF,
                       0xD0, 0xD1, 0xD2, 0xD3, 0xD4, 0xD5, 0xD6, 0xD7, 0xD8, 0xD9, 0xDA, 0xDB, 0xDC, 0xDD, 0xDE, 0xDF,
                       0xE0, 0xE1, 0xE2, 0xE3, 0xE4, 0xE5, 0xE6, 0xE7, 0xE8, 0xE9, 0xEA, 0xEB, 0xEC, 0xED, 0xEE, 0xEF,
                       0xF0, 0xF1, 0xF2, 0xF3, 0xF4, 0xF5, 0xF6, 0xF7, 0xF8, 0xF9, 0xFA, 0xFB, 0xFC, 0xFD, 0xFE, 0xFF };

uint8_t tx_buffer6[BUFFER_SIZE_TX] = {0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F,
                       0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F,
                       0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C, 0x2D, 0x2E, 0x2F,
                       0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x3C, 0x3D, 0x3E, 0x3F,
                       0x40, 0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4A, 0x4B, 0x4C, 0x4D, 0x4E, 0x4F,
                       0x50, 0x51, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57, 0x58, 0x59, 0x5A, 0x5B, 0x5C, 0x5D, 0x5E, 0x5F,
                       0x60, 0x61, 0x62, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68, 0x69, 0x6A, 0x6B, 0x6C, 0x6D, 0x6E, 0x6F,
                       0x70, 0x71, 0x72, 0x73, 0x74, 0x75, 0x76, 0x77, 0x78, 0x79, 0x7A, 0x7B, 0x7C, 0x7D, 0x7E, 0x7F,
                       0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87, 0x88, 0x89, 0x8A, 0x8B, 0x8C, 0x8D, 0x8E, 0x8F,
                       0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97, 0x98, 0x99, 0x9A, 0x9B, 0x9C, 0x9D, 0x9E, 0x9F,
                       0xA0, 0xA1, 0xA2, 0xA3, 0xA4, 0xA5, 0xA6, 0xA7, 0xA8, 0xA9, 0xAA, 0xAB, 0xAC, 0xAD, 0xAE, 0xAF,
                       0xB0, 0xB1, 0xB2, 0xB3, 0xB4, 0xB5, 0xB6, 0xB7, 0xB8, 0xB9, 0xBA, 0xBB, 0xBC, 0xBD, 0xBE, 0xBF,
                       0xC0, 0xC1, 0xC2, 0xC3, 0xC4, 0xC5, 0xC6, 0xC7, 0xC8, 0xC9, 0xCA, 0xCB, 0xCC, 0xCD, 0xCE, 0xCF,
                       0xD0, 0xD1, 0xD2, 0xD3, 0xD4, 0xD5, 0xD6, 0xD7, 0xD8, 0xD9, 0xDA, 0xDB, 0xDC, 0xDD, 0xDE, 0xDF,
                       0xE0, 0xE1, 0xE2, 0xE3, 0xE4, 0xE5, 0xE6, 0xE7, 0xE8, 0xE9, 0xEA, 0xEB, 0xEC, 0xED, 0xEE, 0xEF,
                       0xF0, 0xF1, 0xF2, 0xF3, 0xF4, 0xF5, 0xF6, 0xF7, 0xF8, 0xF9, 0xFA, 0xFB, 0xFC, 0xFD, 0xFE, 0xFF };

//uint8_t rx_buffer[BUFFER_SIZE_RX];
__IO uint16_t tx_counter = 0, rx_counter = 0;
int gbtxcompleted = 1;
uint32_t nbr_data_to_read = BUFFER_SIZE_RX;
uint32_t nbr_data_to_send = BUFFER_SIZE_TX;
unsigned char grxbuffer[U4RX_MAXCOUNT];
int grxlen = 0, grxst = 0;
int gtxcount = 0;
u8 g_Uart6_Rx_Finish=0;

void UART4_IRQHandler(void)
{
    if(RESET != usart_interrupt_flag_get(UART4, USART_INT_FLAG_RBNE)){
        /* read one byte from the receive data register */
		if (grxlen < U4RX_MAXCOUNT) {
			grxbuffer[(grxst + grxlen++) % U4RX_MAXCOUNT] = usart_data_receive(UART4);
		}
		else {
			grxbuffer[(grxst + grxlen) % U4RX_MAXCOUNT] = usart_data_receive(UART4);
		}
        //rx_buffer[rx_counter++] = (uint8_t)usart_data_receive(UART4);
        //if(rx_counter >= nbr_data_to_read)
        //{
		//	rx_counter = 0;
            /* disable the UART4 receive interrupt */
            //usart_interrupt_disable(UART4, USART_INT_RBNE);
        //}
    }

    if(RESET != usart_interrupt_flag_get(UART4, USART_INT_FLAG_TBE)){
        /* write one byte to the transmit data register */
        usart_data_transmit(UART4, tx_buffer[tx_counter++]);

        if(tx_counter >= nbr_data_to_send)
        {
            /* disable the USART0 transmit interrupt */
            usart_interrupt_disable(UART4, USART_INT_TBE);
			tx_counter = 0;
			gbtxcompleted = 1;
            if((g_StartUpdateFirm ==0)&&(goutputmode == 0x10))
            {
              if(g_Outmode==0x11)
                goutputmode = 0x11;
              else if(g_Outmode==0x12)
                goutputmode = 0x12;
            }
        }
    }
}

__IO uint16_t tx_counter6 = 0, rx_counter6 = 0;
int gbtxcompleted6 = 1;
uint32_t nbr_data_to_read6 = BUFFER_SIZE_RX;
uint32_t nbr_data_to_send6 = BUFFER_SIZE_TX;
unsigned char grxbuffer6[U4RX_MAXCOUNT];
int grxlen6 = 0, grxst6 = 0;
int gtxcount6 = 0;
void UART6_IRQHandler(void)
{
    if(RESET != usart_interrupt_flag_get(UART6, USART_INT_FLAG_RBNE)){
        /* read one byte from the receive data register */
		//if (grxlen6 < U4RX_MAXCOUNT) {
			//grxbuffer6[(grxst + grxlen6++) % U4RX_MAXCOUNT] = usart_data_receive(UART6);
		//}
		//else {
			//grxbuffer6[(grxst + grxlen6) % U4RX_MAXCOUNT] = usart_data_receive(UART6);
		//}

		nbr_data_to_read6=64;
        grxbuffer6[rx_counter6++] = (uint8_t)usart_data_receive(UART6);
        if(rx_counter6 >= nbr_data_to_read6)
        {
			rx_counter6 = 0;
			g_Uart6_Rx_Finish=1;
            /* disable the UART6 receive interrupt */
            //usart_interrupt_disable(UART6, USART_INT_RBNE);
        }
    }

    if(RESET != usart_interrupt_flag_get(UART6, USART_INT_FLAG_TBE)){
        /* write one byte to the transmit data register */
        usart_data_transmit(UART6, tx_buffer6[tx_counter6++]);


        if(tx_counter6 >= nbr_data_to_send6)
        {
            /* disable the USART0 transmit interrupt */
            usart_interrupt_disable(UART6, USART_INT_TBE);
			tx_counter6 = 0;
			gbtxcompleted6 = 1;
        }
    }
}


extern	int gbilldebuguart4;
void uart4sendmsg(char *txbuf, int size)
{
	if (gbilldebuguart4 == 0) {
		while(gbtxcompleted == 0);
		gbtxcompleted = 0;
		nbr_data_to_send = size;
		memcpy(tx_buffer, txbuf, size);
		usart_interrupt_enable(UART4, USART_INT_TBE);
		//gbtxcompleted = 0;
	}
}

//固件发送
void UpdateFirmsendmsg(char *txbuf, int size)
{
	//if (gbilldebuguart6 == 0) {
		while(gbtxcompleted6 == 0);
		memset(tx_buffer6,0,BUFFER_SIZE_TX);
		gbtxcompleted6 = 0;
		nbr_data_to_send6 = size;
		memcpy(tx_buffer6, txbuf, size);
		usart_interrupt_enable(UART6, USART_INT_TBE);
		//gbtxcompleted = 0;
	//}
}

	//extern	int gbilldebuguart6;
void uart6sendmsg(char *txbuf, int size)
{
	//if (gbilldebuguart6 == 0) {
		while(gbtxcompleted6 == 0);
		gbtxcompleted6 = 0;
		nbr_data_to_send6 = size;  // 修复：使用正确的变量名
		memcpy(tx_buffer6, txbuf, size);
		usart_interrupt_enable(UART6, USART_INT_TBE);
		//gbtxcompleted = 0;
	//}
}
void uart4sendmsg_billdebug(char *txbuf, int size)
{
	if (gbilldebuguart4 == 1) {
		while(gbtxcompleted == 0);
		gbtxcompleted = 0;
		nbr_data_to_send = size;
		memcpy(tx_buffer, txbuf, size);
		usart_interrupt_enable(UART4, USART_INT_TBE);
		gbtxcompleted = 0;
	}
}


char gprintmsgout[2048];
void printf_uart4(int type, char *fmt, ...)
{
	int size;
	va_list	va;
	while(gbtxcompleted == 0);
	gbtxcompleted = 0;
	va_start(va, fmt);
	memset(gprintmsgout, 0, sizeof(gprintmsgout));
	vsnprintf(gprintmsgout, 2048, fmt, va);
	va_end(va);
	size = strlen(gprintmsgout);
	nbr_data_to_send = size;
	memcpy(tx_buffer, gprintmsgout, size);
	usart_interrupt_enable(UART4, USART_INT_TBE);
	gbtxcompleted = 0;
}
