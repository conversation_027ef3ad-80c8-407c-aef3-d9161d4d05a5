;#<FEEDBACK># ARM Linker, 5060750: Last Updated: Wed Oct 23 17:02:59 2024
;VERSION 0.2
;FILE bmp2.o
bmp2_compensate_data <= USED 0
bmp2_get_power_mode <= USED 0
bmp2_get_sensor_data <= USED 0
bmp2_get_status <= USED 0
;FILE bmp280.o
__asm___8_bmp280_c_b2e809ea____REV16 <= USED 0
__asm___8_bmp280_c_b2e809ea____REVSH <= USED 0
bmp280_get_data <= USED 0
;FILE bsp_adc.o
__asm___9_bsp_adc_c_32916749____REV16 <= USED 0
__asm___9_bsp_adc_c_32916749____REVSH <= USED 0
adc_config <= USED 0
enter_sleep_mode <= USED 0
;FILE bsp_can.o
__asm___9_bsp_can_c_167fbbba____REV16 <= USED 0
__asm___9_bsp_can_c_167fbbba____REVSH <= USED 0
bsp_can_transmit <= USED 0
can_transmit_acc <= USED 0
can_transmit_angle <= USED 0
can_transmit_gyro <= USED 0
can_transmit_h <= USED 0
can_transmit_pos <= USED 0
can_transmit_speed <= USED 0
can_transmit_status <= USED 0
can_transmit_std_heading <= USED 0
can_transmit_temperature <= USED 0
range <= USED 0
stdDev <= USED 0
variance <= USED 0
;FILE bsp_exti.o
__asm___10_bsp_exti_c_hEXTI____REV16 <= USED 0
__asm___10_bsp_exti_c_hEXTI____REVSH <= USED 0
;FILE bsp_flash.o
EndWrite <= USED 0
ReadFlash <= USED 0
ReadFlashByAddr <= USED 0
WriteFlash <= USED 0
WriteOld <= USED 0
__asm___11_bsp_flash_c_g_Addr____REV16 <= USED 0
__asm___11_bsp_flash_c_g_Addr____REVSH <= USED 0
;FILE bsp_fmc.o
DRam_Write <= USED 0
FMC_ReadBuffer <= USED 0
FMC_ReadWord <= USED 0
FMC_WriteBuffer <= USED 0
FMC_WriteWord <= USED 0
Uart_ClearRecvBuffer <= USED 0
Uart_RecvMsg <= USED 0
__asm___9_bsp_fmc_c_2972d6fc____REV16 <= USED 0
__asm___9_bsp_fmc_c_2972d6fc____REVSH <= USED 0
exmc_synchronous_dynamic_ram_init <= USED 0
fill_buffer <= USED 0
fmc_read_16bit_data <= USED 0
fmc_read_32bit_data <= USED 0
fmc_write_16bit_data <= USED 0
fmc_write_32bit_data <= USED 0
sdram_readbuffer_8 <= USED 0
sdram_writebuffer_8 <= USED 0
;FILE bsp_fwdgt.o
__asm___11_bsp_fwdgt_c_eb71a0bb____REV16 <= USED 0
__asm___11_bsp_fwdgt_c_eb71a0bb____REVSH <= USED 0
bsp_fwdgt_feed <= USED 0
bsp_fwdgt_init <= USED 0
;FILE bsp_gpio.o
__asm___10_bsp_gpio_c_aa9f5f93____REV16 <= USED 0
__asm___10_bsp_gpio_c_aa9f5f93____REVSH <= USED 0
;FILE bsp_rtc.o
__asm___9_bsp_rtc_c_pRTC____REV16 <= USED 0
__asm___9_bsp_rtc_c_pRTC____REVSH <= USED 0
getRTCWeekSecond <= USED 0
isLeapYear <= USED 0
yearDay <= USED 0
;FILE bsp_soft_i2c_master.o
__asm___21_bsp_soft_i2c_master_c_fd986fba____REV16 <= USED 0
__asm___21_bsp_soft_i2c_master_c_fd986fba____REVSH <= USED 0
;FILE bsp_sys.o
GetSoftwareVersion <= USED 0
INTX_DISABLE <= USED 0
INTX_ENABLE <= USED 0
IsDCahceEnable <= USED 0
IsICahceEnable <= USED 0
MSR_MSP <= USED 0
Sys_Soft_Reset <= USED 0
WFI_SET <= USED 0
__asm___9_bsp_sys_c_c0b077fc____REV16 <= USED 0
__asm___9_bsp_sys_c_c0b077fc____REVSH <= USED 0
;FILE bsp_tim.o
__asm___9_bsp_tim_c_032080ea____REV16 <= USED 0
__asm___9_bsp_tim_c_032080ea____REVSH <= USED 0
;FILE bsp_uart.o
USART4_IRQHandler <= USED 0
USART6_IRQHandler <= USED 0
__asm___10_bsp_uart_c_addee056____REV16 <= USED 0
__asm___10_bsp_uart_c_addee056____REVSH <= USED 0
bsp_systick_init <= USED 0
bsp_systick_init01 <= USED 0
;FILE can_data.o
__asm___10_can_data_c____REV16 <= USED 0
__asm___10_can_data_c____REVSH <= USED 0
;FILE ch378_hal.o
__asm___11_CH378_HAL_C_3805b1ca____REV16 <= USED 0
__asm___11_CH378_HAL_C_3805b1ca____REVSH <= USED 0
mStopIfError <= USED 0
;FILE ch378_spi_hw.o
SPI_Receive <= USED 0
SPI_Transmit <= USED 0
__asm___14_CH378_SPI_HW_C_139d632a____REV16 <= USED 0
__asm___14_CH378_SPI_HW_C_139d632a____REVSH <= USED 0
;FILE ch395cmd.o
CH395CMDGetGlobIntStatus <= USED 0
CH395CMDGetGlobIntStatus_ALL <= USED 0
CH395CMDGetMACAddr <= USED 0
CH395CMDGetPHYStatus <= USED 0
CH395CMDGetRemoteIPP <= USED 0
CH395CMDGetSocketStatus <= USED 0
CH395CMDGetUnreachIPPT <= USED 0
CH395CMDGetVer <= USED 0
CH395CMDReset <= USED 0
CH395CMDSetMACAddr <= USED 0
CH395CMDSetMACFilt <= USED 0
CH395CMDSetPHY <= USED 0
CH395CMDSetRetryCount <= USED 0
CH395CMDSetRetryPeriod <= USED 0
CH395CMDSetUartBaudRate <= USED 0
CH395CMDSleep <= USED 0
CH395CRCRet6Bit <= USED 0
CH395ClearRecvBuf <= USED 0
CH395CloseSocket <= USED 0
CH395DHCPEnable <= USED 0
CH395EEPROMErase <= USED 0
CH395EEPROMRead <= USED 0
CH395EEPROMWrite <= USED 0
CH395EnablePing <= USED 0
CH395GetDHCPStatus <= USED 0
CH395GetIPInf <= USED 0
CH395GetRecvData <= USED 0
CH395GetRecvLength <= USED 0
CH395GetSocketInt <= USED 0
CH395OpenSocket <= USED 0
CH395ReadGPIOAddr <= USED 0
CH395SendData <= USED 0
CH395SetSocketDesIP <= USED 0
CH395SetSocketDesPort <= USED 0
CH395SetSocketIPRAWProto <= USED 0
CH395SetSocketProtType <= USED 0
CH395SetSocketSourPort <= USED 0
CH395TCPConnect <= USED 0
CH395TCPDisconnect <= USED 0
CH395TCPListen <= USED 0
CH395UDPSendTo <= USED 0
CH395WriteGPIOAddr <= USED 0
__asm___10_CH395CMD_C_39ed8502____REV16 <= USED 0
__asm___10_CH395CMD_C_39ed8502____REVSH <= USED 0
;FILE ch395spi.o
Query395Interrupt <= USED 0
__asm___10_CH395SPI_C_mDelayuS____REV16 <= USED 0
__asm___10_CH395SPI_C_mDelayuS____REVSH <= USED 0
;FILE clock.o
__asm___7_clock_c_bbbbd896____REV16 <= USED 0
__asm___7_clock_c_bbbbd896____REVSH <= USED 0
ins_tick_increase <= USED 0
ins_tick_set <= USED 0
;FILE common.o
__asm___8_common_c_2dee1649____REV16 <= USED 0
__asm___8_common_c_2dee1649____REVSH <= USED 0
;FILE computerframeparse.o
IMU_test_data_send <= USED 0
__asm___20_computerFrameParse_c_0810e648____REV16 <= USED 0
__asm___20_computerFrameParse_c_0810e648____REVSH <= USED 0
adj_paraSave <= USED 0
comm_calib <= USED 0
comm_get_baseline <= USED 0
comm_param_clrbits <= USED 0
comm_readAccelCaliPtr <= USED 0
comm_readGyroCaliPtr <= USED 0
comm_read_currentFrameType <= USED 0
comm_read_dataMode <= USED 0
miscell_handle <= USED 0
navi_test_statusRd <= USED 0
;FILE data_convert.o
GetDouble <= USED 0
GetNumber <= USED 0
InvertUint16 <= USED 0
InvertUint32 <= USED 0
InvertUint8 <= USED 0
SignedToBuf <= USED 0
UnsignedToBuf <= USED 0
bufToFloat <= USED 0
bufToSigned <= USED 0
bufToU32 <= USED 0
bufToUnsigned <= USED 0
char2strbin <= USED 0
data24BitToSigned <= USED 0
dec2HexStr <= USED 0
double2Hex <= USED 0
dtoc <= USED 0
float2Hex <= USED 0
floatToBuf <= USED 0
ftoa <= USED 0
hex2Double <= USED 0
int2strbin <= USED 0
itoa <= USED 0
itoc <= USED 0
short2strbin <= USED 0
str2bin <= USED 0
str2lower <= USED 0
strInStrCount <= USED 0
strReplace <= USED 0
strReverse <= USED 0
strSplit <= USED 0
strbin2u16 <= USED 0
strt2upper <= USED 0
u32ToBuf <= USED 0
;FILE data_shift.o
ProcessIMUDataUserAxis <= USED 0
ProcessINS <= USED 0
ProcessINSBootMode <= USED 0
ProcessINSWorkMode <= USED 0
__asm___12_Data_shift_c_0997f2fe____REV16 <= USED 0
__asm___12_Data_shift_c_0997f2fe____REVSH <= USED 0
;FILE file_sys.o
CH378AutoEnumDevice <= USED 0
CH378AutoInitDisk <= USED 0
CH378AutoResetDisk <= USED 0
CH378BlockOnlyCMD <= USED 0
CH378ByteRead <= USED 0
CH378ByteReadPrepare <= USED 0
CH378ByteWriteExecute <= USED 0
CH378CheckExist <= USED 0
CH378ClearStall <= USED 0
CH378DirCreate <= USED 0
CH378DirInfoRead <= USED 0
CH378DirInfoSave <= USED 0
CH378DiskCapacity <= USED 0
CH378DiskConnect <= USED 0
CH378DiskQuery <= USED 0
CH378DiskReadSec <= USED 0
CH378DiskReady <= USED 0
CH378DiskWriteSec <= USED 0
CH378EnterFullSleep <= USED 0
CH378EnterHalfSleep <= USED 0
CH378FileClose <= USED 0
CH378FileErase <= USED 0
CH378FileModify <= USED 0
CH378FileQuery <= USED 0
CH378GetDiskInquiry <= USED 0
CH378GetDiskReady <= USED 0
CH378GetDiskReqSense <= USED 0
CH378GetDiskSize <= USED 0
CH378GetFileSize <= USED 0
CH378GetTrueLen <= USED 0
CH378HardwareReset <= USED 0
CH378Read32bitDat <= USED 0
CH378ReadBlock <= USED 0
CH378ReadOfsBlock <= USED 0
CH378ReadReqBlock <= USED 0
CH378ReadVar32 <= USED 0
CH378SecLocate <= USED 0
CH378SecRead <= USED 0
CH378SecWrite <= USED 0
CH378SelfEnumDevice <= USED 0
CH378SendCmdDatWaitInt <= USED 0
CH378SetFileSize <= USED 0
CH378WriteBlock <= USED 0
CH378WriteVar32 <= USED 0
CH378WriteVar8 <= USED 0
__asm___10_FILE_SYS_C_ed1898de____REV16 <= USED 0
__asm___10_FILE_SYS_C_ed1898de____REVSH <= USED 0
;FILE firmwareupdatefile.o
Drv_FlashWrite1 <= USED 0
FlashTest <= USED 0
Get_CRC8 <= USED 0
UartWrite <= USED 0
__asm___20_FirmwareUpdateFile_c_250fdb8e____REV16 <= USED 0
__asm___20_FirmwareUpdateFile_c_250fdb8e____REVSH <= USED 0
;FILE fpgad.o
__asm___7_fpgad_c____REV16 <= USED 0
__asm___7_fpgad_c____REVSH <= USED 0
;FILE frame_analysis.o
__asm___16_frame_analysis_c_d83ae75a____REV16 <= USED 0
__asm___16_frame_analysis_c_d83ae75a____REVSH <= USED 0
frame_fill_ifog <= USED 0
frame_fill_imu <= USED 0
frame_iPMV_pack_and_send <= USED 0
frame_init <= USED 0
frame_writeDram <= USED 0
sum_check <= USED 0
syn_arm2 <= USED 0
;FILE gd32f4xx_adc.o
__asm___14_gd32f4xx_adc_c_725e678f____REV16 <= USED 0
__asm___14_gd32f4xx_adc_c_725e678f____REVSH <= USED 0
adc_calibration_enable <= USED 0
adc_channel_16_to_18 <= USED 0
adc_channel_length_config <= USED 0
adc_clock_config <= USED 0
adc_data_alignment_config <= USED 0
adc_deinit <= USED 0
adc_disable <= USED 0
adc_discontinuous_mode_config <= USED 0
adc_dma_mode_disable <= USED 0
adc_dma_mode_enable <= USED 0
adc_dma_request_after_last_disable <= USED 0
adc_dma_request_after_last_enable <= USED 0
adc_enable <= USED 0
adc_end_of_conversion_config <= USED 0
adc_external_trigger_config <= USED 0
adc_external_trigger_source_config <= USED 0
adc_flag_clear <= USED 0
adc_flag_get <= USED 0
adc_inserted_channel_config <= USED 0
adc_inserted_channel_offset_config <= USED 0
adc_inserted_data_read <= USED 0
adc_inserted_software_startconv_flag_get <= USED 0
adc_interrupt_disable <= USED 0
adc_interrupt_enable <= USED 0
adc_interrupt_flag_get <= USED 0
adc_oversample_mode_config <= USED 0
adc_oversample_mode_disable <= USED 0
adc_oversample_mode_enable <= USED 0
adc_regular_channel_config <= USED 0
adc_regular_data_read <= USED 0
adc_regular_software_startconv_flag_get <= USED 0
adc_resolution_config <= USED 0
adc_software_trigger_enable <= USED 0
adc_special_function_config <= USED 0
adc_sync_delay_config <= USED 0
adc_sync_dma_config <= USED 0
adc_sync_dma_request_after_last_disable <= USED 0
adc_sync_dma_request_after_last_enable <= USED 0
adc_sync_mode_config <= USED 0
adc_sync_regular_data_read <= USED 0
adc_watchdog_disable <= USED 0
adc_watchdog_group_channel_enable <= USED 0
adc_watchdog_single_channel_disable <= USED 0
adc_watchdog_single_channel_enable <= USED 0
adc_watchdog_threshold_config <= USED 0
;FILE gd32f4xx_can.o
__asm___14_gd32f4xx_can_c_a739e72c____REV16 <= USED 0
__asm___14_gd32f4xx_can_c_a739e72c____REVSH <= USED 0
can1_filter_start_bank <= USED 0
can_debug_freeze_disable <= USED 0
can_debug_freeze_enable <= USED 0
can_error_get <= USED 0
can_fifo_release <= USED 0
can_flag_clear <= USED 0
can_flag_get <= USED 0
can_interrupt_disable <= USED 0
can_interrupt_flag_clear <= USED 0
can_interrupt_flag_get <= USED 0
can_message_transmit <= USED 0
can_receive_error_number_get <= USED 0
can_receive_message_length_get <= USED 0
can_struct_para_init <= USED 0
can_time_trigger_mode_disable <= USED 0
can_time_trigger_mode_enable <= USED 0
can_transmission_stop <= USED 0
can_transmit_error_number_get <= USED 0
can_transmit_states <= USED 0
can_wakeup <= USED 0
can_working_mode_set <= USED 0
;FILE gd32f4xx_crc.o
__asm___14_gd32f4xx_crc_c_a9718c49____REV16 <= USED 0
__asm___14_gd32f4xx_crc_c_a9718c49____REVSH <= USED 0
crc_block_data_calculate <= USED 0
crc_data_register_read <= USED 0
crc_data_register_reset <= USED 0
crc_deinit <= USED 0
crc_free_data_register_read <= USED 0
crc_free_data_register_write <= USED 0
crc_single_data_calculate <= USED 0
;FILE gd32f4xx_ctc.o
__asm___14_gd32f4xx_ctc_c_da6bf5c3____REV16 <= USED 0
__asm___14_gd32f4xx_ctc_c_da6bf5c3____REVSH <= USED 0
ctc_clock_limit_value_config <= USED 0
ctc_counter_capture_value_read <= USED 0
ctc_counter_direction_read <= USED 0
ctc_counter_disable <= USED 0
ctc_counter_enable <= USED 0
ctc_counter_reload_value_config <= USED 0
ctc_counter_reload_value_read <= USED 0
ctc_deinit <= USED 0
ctc_flag_clear <= USED 0
ctc_flag_get <= USED 0
ctc_hardware_trim_mode_config <= USED 0
ctc_interrupt_disable <= USED 0
ctc_interrupt_enable <= USED 0
ctc_interrupt_flag_clear <= USED 0
ctc_interrupt_flag_get <= USED 0
ctc_irc48m_trim_value_config <= USED 0
ctc_irc48m_trim_value_read <= USED 0
ctc_refsource_polarity_config <= USED 0
ctc_refsource_prescaler_config <= USED 0
ctc_refsource_signal_select <= USED 0
ctc_software_refsource_pulse_generate <= USED 0
ctc_usbsof_signal_select <= USED 0
;FILE gd32f4xx_dac.o
__asm___14_gd32f4xx_dac_c_dee0e984____REV16 <= USED 0
__asm___14_gd32f4xx_dac_c_dee0e984____REVSH <= USED 0
dac_concurrent_data_set <= USED 0
dac_concurrent_disable <= USED 0
dac_concurrent_enable <= USED 0
dac_concurrent_interrupt_disable <= USED 0
dac_concurrent_interrupt_enable <= USED 0
dac_concurrent_output_buffer_disable <= USED 0
dac_concurrent_output_buffer_enable <= USED 0
dac_concurrent_software_trigger_disable <= USED 0
dac_concurrent_software_trigger_enable <= USED 0
dac_data_set <= USED 0
dac_deinit <= USED 0
dac_disable <= USED 0
dac_dma_disable <= USED 0
dac_dma_enable <= USED 0
dac_enable <= USED 0
dac_flag_clear <= USED 0
dac_flag_get <= USED 0
dac_interrupt_disable <= USED 0
dac_interrupt_enable <= USED 0
dac_interrupt_flag_clear <= USED 0
dac_interrupt_flag_get <= USED 0
dac_lfsr_noise_config <= USED 0
dac_output_buffer_disable <= USED 0
dac_output_buffer_enable <= USED 0
dac_output_value_get <= USED 0
dac_software_trigger_disable <= USED 0
dac_software_trigger_enable <= USED 0
dac_triangle_noise_config <= USED 0
dac_trigger_disable <= USED 0
dac_trigger_enable <= USED 0
dac_trigger_source_config <= USED 0
dac_wave_bit_width_config <= USED 0
dac_wave_mode_config <= USED 0
;FILE gd32f4xx_dbg.o
__asm___14_gd32f4xx_dbg_c_6327dbbb____REV16 <= USED 0
__asm___14_gd32f4xx_dbg_c_6327dbbb____REVSH <= USED 0
dbg_deinit <= USED 0
dbg_id_get <= USED 0
dbg_low_power_disable <= USED 0
dbg_low_power_enable <= USED 0
dbg_periph_disable <= USED 0
dbg_periph_enable <= USED 0
dbg_trace_pin_disable <= USED 0
dbg_trace_pin_enable <= USED 0
;FILE gd32f4xx_dci.o
__asm___14_gd32f4xx_dci_c_61d6ddca____REV16 <= USED 0
__asm___14_gd32f4xx_dci_c_61d6ddca____REVSH <= USED 0
dci_capture_disable <= USED 0
dci_capture_enable <= USED 0
dci_crop_window_config <= USED 0
dci_crop_window_disable <= USED 0
dci_crop_window_enable <= USED 0
dci_data_read <= USED 0
dci_deinit <= USED 0
dci_disable <= USED 0
dci_embedded_sync_disable <= USED 0
dci_embedded_sync_enable <= USED 0
dci_enable <= USED 0
dci_flag_get <= USED 0
dci_init <= USED 0
dci_interrupt_disable <= USED 0
dci_interrupt_enable <= USED 0
dci_interrupt_flag_clear <= USED 0
dci_interrupt_flag_get <= USED 0
dci_jpeg_disable <= USED 0
dci_jpeg_enable <= USED 0
dci_sync_codes_config <= USED 0
dci_sync_codes_unmask_config <= USED 0
;FILE gd32f4xx_dma.o
__asm___14_gd32f4xx_dma_c_7af11ded____REV16 <= USED 0
__asm___14_gd32f4xx_dma_c_7af11ded____REVSH <= USED 0
dma_channel_disable <= USED 0
dma_channel_enable <= USED 0
dma_channel_subperipheral_select <= USED 0
dma_circulation_disable <= USED 0
dma_circulation_enable <= USED 0
dma_deinit <= USED 0
dma_fifo_status_get <= USED 0
dma_flag_clear <= USED 0
dma_flag_get <= USED 0
dma_flow_controller_config <= USED 0
dma_interrupt_disable <= USED 0
dma_interrupt_enable <= USED 0
dma_interrupt_flag_clear <= USED 0
dma_interrupt_flag_get <= USED 0
dma_memory_address_config <= USED 0
dma_memory_address_generation_config <= USED 0
dma_memory_burst_beats_config <= USED 0
dma_memory_width_config <= USED 0
dma_multi_data_mode_init <= USED 0
dma_multi_data_para_struct_init <= USED 0
dma_periph_address_config <= USED 0
dma_periph_burst_beats_config <= USED 0
dma_periph_width_config <= USED 0
dma_peripheral_address_generation_config <= USED 0
dma_priority_config <= USED 0
dma_single_data_mode_init <= USED 0
dma_single_data_para_struct_init <= USED 0
dma_switch_buffer_mode_config <= USED 0
dma_switch_buffer_mode_enable <= USED 0
dma_transfer_direction_config <= USED 0
dma_transfer_number_config <= USED 0
dma_transfer_number_get <= USED 0
dma_using_memory_get <= USED 0
;FILE gd32f4xx_enet.o
__asm___15_gd32f4xx_enet_c_2016197e____REV16 <= USED 0
__asm___15_gd32f4xx_enet_c_2016197e____REVSH <= USED 0
enet_address_filter_config <= USED 0
enet_address_filter_disable <= USED 0
enet_address_filter_enable <= USED 0
enet_current_desc_address_get <= USED 0
enet_debug_status_get <= USED 0
enet_deinit <= USED 0
enet_desc_flag_clear <= USED 0
enet_desc_flag_get <= USED 0
enet_desc_flag_set <= USED 0
enet_desc_information_get <= USED 0
enet_desc_select_normal_mode <= USED 0
enet_descriptors_chain_init <= USED 0
enet_descriptors_ring_init <= USED 0
enet_disable <= USED 0
enet_dma_feature_disable <= USED 0
enet_dma_feature_enable <= USED 0
enet_dmaprocess_resume <= USED 0
enet_dmaprocess_state_get <= USED 0
enet_enable <= USED 0
enet_flag_clear <= USED 0
enet_flag_get <= USED 0
enet_fliter_feature_disable <= USED 0
enet_fliter_feature_enable <= USED 0
enet_flowcontrol_feature_disable <= USED 0
enet_flowcontrol_feature_enable <= USED 0
enet_flowcontrol_threshold_config <= USED 0
enet_forward_feature_disable <= USED 0
enet_forward_feature_enable <= USED 0
enet_frame_receive <= USED 0
enet_frame_transmit <= USED 0
enet_init <= USED 0
enet_initpara_config <= USED 0
enet_initpara_reset <= USED 0
enet_interrupt_disable <= USED 0
enet_interrupt_enable <= USED 0
enet_interrupt_flag_clear <= USED 0
enet_interrupt_flag_get <= USED 0
enet_mac_address_get <= USED 0
enet_mac_address_set <= USED 0
enet_missed_frame_counter_get <= USED 0
enet_msc_counters_get <= USED 0
enet_msc_counters_preset_config <= USED 0
enet_msc_counters_reset <= USED 0
enet_msc_feature_disable <= USED 0
enet_msc_feature_enable <= USED 0
enet_pauseframe_config <= USED 0
enet_pauseframe_detect_config <= USED 0
enet_pauseframe_generate <= USED 0
enet_phy_config <= USED 0
enet_phy_write_read <= USED 0
enet_phyloopback_disable <= USED 0
enet_phyloopback_enable <= USED 0
enet_ptp_expected_time_config <= USED 0
enet_ptp_feature_disable <= USED 0
enet_ptp_feature_enable <= USED 0
enet_ptp_normal_descriptors_chain_init <= USED 0
enet_ptp_normal_descriptors_ring_init <= USED 0
enet_ptp_pps_output_frequency_config <= USED 0
enet_ptp_subsecond_increment_config <= USED 0
enet_ptp_system_time_get <= USED 0
enet_ptp_timestamp_addend_config <= USED 0
enet_ptp_timestamp_function_config <= USED 0
enet_ptp_timestamp_update_config <= USED 0
enet_ptpframe_receive_normal_mode <= USED 0
enet_ptpframe_transmit_normal_mode <= USED 0
enet_registers_get <= USED 0
enet_rx_desc_delay_receive_complete_interrupt <= USED 0
enet_rx_desc_immediate_receive_complete_interrupt <= USED 0
enet_rx_disable <= USED 0
enet_rx_enable <= USED 0
enet_rxframe_drop <= USED 0
enet_rxframe_size_get <= USED 0
enet_rxprocess_check_recovery <= USED 0
enet_software_reset <= USED 0
enet_transmit_checksum_config <= USED 0
enet_tx_disable <= USED 0
enet_tx_enable <= USED 0
enet_txfifo_flush <= USED 0
enet_wum_feature_disable <= USED 0
enet_wum_feature_enable <= USED 0
enet_wum_filter_config <= USED 0
enet_wum_filter_register_pointer_reset <= USED 0
;FILE gd32f4xx_exmc.o
__asm___15_gd32f4xx_exmc_c_39d0925f____REV16 <= USED 0
__asm___15_gd32f4xx_exmc_c_39d0925f____REVSH <= USED 0
exmc_ecc_get <= USED 0
exmc_flag_clear <= USED 0
exmc_flag_get <= USED 0
exmc_interrupt_disable <= USED 0
exmc_interrupt_enable <= USED 0
exmc_interrupt_flag_clear <= USED 0
exmc_interrupt_flag_get <= USED 0
exmc_nand_deinit <= USED 0
exmc_nand_disable <= USED 0
exmc_nand_ecc_config <= USED 0
exmc_nand_enable <= USED 0
exmc_nand_init <= USED 0
exmc_nand_struct_para_init <= USED 0
exmc_norsram_consecutive_clock_config <= USED 0
exmc_norsram_deinit <= USED 0
exmc_norsram_disable <= USED 0
exmc_norsram_page_size_config <= USED 0
exmc_norsram_struct_para_init <= USED 0
exmc_pccard_deinit <= USED 0
exmc_pccard_disable <= USED 0
exmc_pccard_enable <= USED 0
exmc_pccard_init <= USED 0
exmc_pccard_struct_para_init <= USED 0
exmc_sdram_autorefresh_number_set <= USED 0
exmc_sdram_bankstatus_get <= USED 0
exmc_sdram_command_config <= USED 0
exmc_sdram_deinit <= USED 0
exmc_sdram_init <= USED 0
exmc_sdram_readsample_config <= USED 0
exmc_sdram_readsample_enable <= USED 0
exmc_sdram_refresh_count_set <= USED 0
exmc_sdram_struct_para_init <= USED 0
exmc_sdram_write_protection_config <= USED 0
exmc_sqpipsram_deinit <= USED 0
exmc_sqpipsram_high_id_get <= USED 0
exmc_sqpipsram_init <= USED 0
exmc_sqpipsram_low_id_get <= USED 0
exmc_sqpipsram_read_command_set <= USED 0
exmc_sqpipsram_read_id_command_send <= USED 0
exmc_sqpipsram_send_command_state_get <= USED 0
exmc_sqpipsram_struct_para_init <= USED 0
exmc_sqpipsram_write_cmd_send <= USED 0
exmc_sqpipsram_write_command_set <= USED 0
;FILE gd32f4xx_exti.o
__asm___15_gd32f4xx_exti_c_3d6b70f4____REV16 <= USED 0
__asm___15_gd32f4xx_exti_c_3d6b70f4____REVSH <= USED 0
exti_deinit <= USED 0
exti_event_disable <= USED 0
exti_event_enable <= USED 0
exti_flag_clear <= USED 0
exti_flag_get <= USED 0
exti_interrupt_disable <= USED 0
exti_interrupt_enable <= USED 0
exti_software_interrupt_disable <= USED 0
exti_software_interrupt_enable <= USED 0
;FILE gd32f4xx_fmc.o
__asm___14_gd32f4xx_fmc_c_2e62a613____REV16 <= USED 0
__asm___14_gd32f4xx_fmc_c_2e62a613____REVSH <= USED 0
fmc_bank0_erase <= USED 0
fmc_bank1_erase <= USED 0
fmc_flag_get <= USED 0
fmc_halfword_program <= USED 0
fmc_interrupt_disable <= USED 0
fmc_interrupt_enable <= USED 0
fmc_interrupt_flag_clear <= USED 0
fmc_interrupt_flag_get <= USED 0
fmc_mass_erase <= USED 0
fmc_page_erase <= USED 0
fmc_wscnt_set <= USED 0
ob_boot_mode_config <= USED 0
ob_drp0_get <= USED 0
ob_drp1_get <= USED 0
ob_drp_disable <= USED 0
ob_drp_enable <= USED 0
ob_erase <= USED 0
ob_lock <= USED 0
ob_security_protection_config <= USED 0
ob_spc_get <= USED 0
ob_start <= USED 0
ob_unlock <= USED 0
ob_user_bor_threshold <= USED 0
ob_user_bor_threshold_get <= USED 0
ob_user_get <= USED 0
ob_user_write <= USED 0
ob_write_protection0_get <= USED 0
ob_write_protection1_get <= USED 0
ob_write_protection_disable <= USED 0
ob_write_protection_enable <= USED 0
;FILE gd32f4xx_fwdgt.o
__asm___16_gd32f4xx_fwdgt_c_5a293c79____REV16 <= USED 0
__asm___16_gd32f4xx_fwdgt_c_5a293c79____REVSH <= USED 0
fwdgt_config <= USED 0
fwdgt_counter_reload <= USED 0
fwdgt_enable <= USED 0
fwdgt_flag_get <= USED 0
fwdgt_write_disable <= USED 0
fwdgt_write_enable <= USED 0
;FILE gd32f4xx_gpio.o
__asm___15_gd32f4xx_gpio_c_ed302560____REV16 <= USED 0
__asm___15_gd32f4xx_gpio_c_ed302560____REVSH <= USED 0
gpio_bit_toggle <= USED 0
gpio_bit_write <= USED 0
gpio_deinit <= USED 0
gpio_input_port_get <= USED 0
gpio_output_bit_get <= USED 0
gpio_output_port_get <= USED 0
gpio_pin_lock <= USED 0
gpio_port_toggle <= USED 0
gpio_port_write <= USED 0
;FILE gd32f4xx_i2c.o
__asm___14_gd32f4xx_i2c_c_60c11f87____REV16 <= USED 0
__asm___14_gd32f4xx_i2c_c_60c11f87____REVSH <= USED 0
i2c_ack_config <= USED 0
i2c_ackpos_config <= USED 0
i2c_analog_noise_filter_disable <= USED 0
i2c_analog_noise_filter_enable <= USED 0
i2c_clock_config <= USED 0
i2c_data_receive <= USED 0
i2c_data_transmit <= USED 0
i2c_deinit <= USED 0
i2c_digital_noise_filter_config <= USED 0
i2c_disable <= USED 0
i2c_dma_config <= USED 0
i2c_dma_last_transfer_config <= USED 0
i2c_dualaddr_disable <= USED 0
i2c_dualaddr_enable <= USED 0
i2c_enable <= USED 0
i2c_flag_clear <= USED 0
i2c_flag_get <= USED 0
i2c_interrupt_disable <= USED 0
i2c_interrupt_enable <= USED 0
i2c_interrupt_flag_clear <= USED 0
i2c_interrupt_flag_get <= USED 0
i2c_master_addressing <= USED 0
i2c_mode_addr_config <= USED 0
i2c_pec_config <= USED 0
i2c_pec_transfer_config <= USED 0
i2c_pec_value_get <= USED 0
i2c_sam_disable <= USED 0
i2c_sam_enable <= USED 0
i2c_sam_timeout_disable <= USED 0
i2c_sam_timeout_enable <= USED 0
i2c_slave_response_to_gcall_config <= USED 0
i2c_smbus_alert_config <= USED 0
i2c_smbus_arp_config <= USED 0
i2c_smbus_type_config <= USED 0
i2c_software_reset_config <= USED 0
i2c_start_on_bus <= USED 0
i2c_stop_on_bus <= USED 0
i2c_stretch_scl_low_config <= USED 0
;FILE gd32f4xx_ipa.o
__asm___14_gd32f4xx_ipa_c_d280337b____REV16 <= USED 0
__asm___14_gd32f4xx_ipa_c_d280337b____REVSH <= USED 0
ipa_background_init <= USED 0
ipa_background_lut_init <= USED 0
ipa_background_lut_loading_enable <= USED 0
ipa_background_struct_para_init <= USED 0
ipa_deinit <= USED 0
ipa_destination_init <= USED 0
ipa_destination_struct_para_init <= USED 0
ipa_flag_clear <= USED 0
ipa_flag_get <= USED 0
ipa_foreground_init <= USED 0
ipa_foreground_lut_init <= USED 0
ipa_foreground_lut_loading_enable <= USED 0
ipa_foreground_struct_para_init <= USED 0
ipa_inter_timer_config <= USED 0
ipa_interrupt_disable <= USED 0
ipa_interrupt_enable <= USED 0
ipa_interrupt_flag_clear <= USED 0
ipa_interrupt_flag_get <= USED 0
ipa_interval_clock_num_config <= USED 0
ipa_line_mark_config <= USED 0
ipa_pixel_format_convert_mode_set <= USED 0
ipa_transfer_enable <= USED 0
ipa_transfer_hangup_disable <= USED 0
ipa_transfer_hangup_enable <= USED 0
ipa_transfer_stop_disable <= USED 0
ipa_transfer_stop_enable <= USED 0
;FILE gd32f4xx_iref.o
__asm___15_gd32f4xx_iref_c_42c83664____REV16 <= USED 0
__asm___15_gd32f4xx_iref_c_42c83664____REVSH <= USED 0
iref_deinit <= USED 0
iref_disable <= USED 0
iref_enable <= USED 0
iref_mode_set <= USED 0
iref_precision_trim_value_set <= USED 0
iref_sink_set <= USED 0
iref_step_data_config <= USED 0
;FILE gd32f4xx_it.o
__asm___13_gd32f4xx_it_c_bb8ca80c____REV16 <= USED 0
__asm___13_gd32f4xx_it_c_bb8ca80c____REVSH <= USED 0
printf_uart4 <= USED 0
uart4sendmsg_billdebug <= USED 0
;FILE gd32f4xx_misc.o
__asm___15_gd32f4xx_misc_c_25ea0539____REV16 <= USED 0
__asm___15_gd32f4xx_misc_c_25ea0539____REVSH <= USED 0
system_lowpower_reset <= USED 0
system_lowpower_set <= USED 0
;FILE gd32f4xx_pmu.o
__asm___14_gd32f4xx_pmu_c_ac4503ae____REV16 <= USED 0
__asm___14_gd32f4xx_pmu_c_ac4503ae____REVSH <= USED 0
pmu_backup_ldo_config <= USED 0
pmu_backup_write_disable <= USED 0
pmu_deinit <= USED 0
pmu_flag_clear <= USED 0
pmu_flag_get <= USED 0
pmu_highdriver_mode_disable <= USED 0
pmu_highdriver_mode_enable <= USED 0
pmu_highdriver_switch_select <= USED 0
pmu_ldo_output_select <= USED 0
pmu_lowdriver_mode_disable <= USED 0
pmu_lowdriver_mode_enable <= USED 0
pmu_lowpower_driver_config <= USED 0
pmu_lvd_disable <= USED 0
pmu_lvd_select <= USED 0
pmu_normalpower_driver_config <= USED 0
pmu_to_deepsleepmode <= USED 0
pmu_to_sleepmode <= USED 0
pmu_to_standbymode <= USED 0
pmu_wakeup_pin_disable <= USED 0
pmu_wakeup_pin_enable <= USED 0
;FILE gd32f4xx_rcu.o
__asm___14_gd32f4xx_rcu_c_60720801____REV16 <= USED 0
__asm___14_gd32f4xx_rcu_c_60720801____REVSH <= USED 0
rcu_ahb_clock_config <= USED 0
rcu_apb1_clock_config <= USED 0
rcu_apb2_clock_config <= USED 0
rcu_bkp_reset_disable <= USED 0
rcu_bkp_reset_enable <= USED 0
rcu_ck48m_clock_config <= USED 0
rcu_ckout0_config <= USED 0
rcu_ckout1_config <= USED 0
rcu_deepsleep_voltage_set <= USED 0
rcu_deinit <= USED 0
rcu_hxtal_clock_monitor_disable <= USED 0
rcu_hxtal_clock_monitor_enable <= USED 0
rcu_i2s_clock_config <= USED 0
rcu_interrupt_disable <= USED 0
rcu_interrupt_enable <= USED 0
rcu_interrupt_flag_clear <= USED 0
rcu_interrupt_flag_get <= USED 0
rcu_irc16m_adjust_value_set <= USED 0
rcu_lxtal_drive_capability_config <= USED 0
rcu_osci_bypass_mode_disable <= USED 0
rcu_osci_bypass_mode_enable <= USED 0
rcu_osci_off <= USED 0
rcu_periph_clock_disable <= USED 0
rcu_periph_clock_sleep_disable <= USED 0
rcu_periph_clock_sleep_enable <= USED 0
rcu_pll48m_clock_config <= USED 0
rcu_pll_config <= USED 0
rcu_plli2s_config <= USED 0
rcu_pllsai_config <= USED 0
rcu_rtc_div_config <= USED 0
rcu_spread_spectrum_config <= USED 0
rcu_spread_spectrum_disable <= USED 0
rcu_spread_spectrum_enable <= USED 0
rcu_system_clock_source_config <= USED 0
rcu_system_clock_source_get <= USED 0
rcu_tli_clock_div_config <= USED 0
rcu_voltage_key_unlock <= USED 0
;FILE gd32f4xx_rtc.o
__asm___14_gd32f4xx_rtc_c_12071f15____REV16 <= USED 0
__asm___14_gd32f4xx_rtc_c_12071f15____REVSH <= USED 0
rtc_alarm_config <= USED 0
rtc_alarm_disable <= USED 0
rtc_alarm_enable <= USED 0
rtc_alarm_get <= USED 0
rtc_alarm_output_config <= USED 0
rtc_alarm_subsecond_config <= USED 0
rtc_alarm_subsecond_get <= USED 0
rtc_bypass_shadow_disable <= USED 0
rtc_bypass_shadow_enable <= USED 0
rtc_calibration_output_config <= USED 0
rtc_coarse_calibration_config <= USED 0
rtc_coarse_calibration_disable <= USED 0
rtc_coarse_calibration_enable <= USED 0
rtc_current_time_get <= USED 0
rtc_deinit <= USED 0
rtc_hour_adjust <= USED 0
rtc_interrupt_disable <= USED 0
rtc_refclock_detection_disable <= USED 0
rtc_refclock_detection_enable <= USED 0
rtc_second_adjust <= USED 0
rtc_smooth_calibration_config <= USED 0
rtc_subsecond_get <= USED 0
rtc_tamper0_pin_map <= USED 0
rtc_tamper_disable <= USED 0
rtc_tamper_enable <= USED 0
rtc_timestamp_disable <= USED 0
rtc_timestamp_pin_map <= USED 0
rtc_wakeup_clock_set <= USED 0
rtc_wakeup_disable <= USED 0
rtc_wakeup_enable <= USED 0
rtc_wakeup_timer_get <= USED 0
rtc_wakeup_timer_set <= USED 0
;FILE gd32f4xx_sdio.o
__asm___15_gd32f4xx_sdio_c_dd5ab3bb____REV16 <= USED 0
__asm___15_gd32f4xx_sdio_c_dd5ab3bb____REVSH <= USED 0
sdio_bus_mode_set <= USED 0
sdio_ceata_command_completion_disable <= USED 0
sdio_ceata_command_completion_enable <= USED 0
sdio_ceata_command_disable <= USED 0
sdio_ceata_command_enable <= USED 0
sdio_ceata_interrupt_disable <= USED 0
sdio_ceata_interrupt_enable <= USED 0
sdio_clock_config <= USED 0
sdio_clock_disable <= USED 0
sdio_clock_enable <= USED 0
sdio_command_index_get <= USED 0
sdio_command_response_config <= USED 0
sdio_csm_disable <= USED 0
sdio_csm_enable <= USED 0
sdio_data_config <= USED 0
sdio_data_counter_get <= USED 0
sdio_data_read <= USED 0
sdio_data_transfer_config <= USED 0
sdio_data_write <= USED 0
sdio_deinit <= USED 0
sdio_dma_disable <= USED 0
sdio_dma_enable <= USED 0
sdio_dsm_disable <= USED 0
sdio_dsm_enable <= USED 0
sdio_fifo_counter_get <= USED 0
sdio_flag_clear <= USED 0
sdio_flag_get <= USED 0
sdio_hardware_clock_disable <= USED 0
sdio_hardware_clock_enable <= USED 0
sdio_interrupt_disable <= USED 0
sdio_interrupt_enable <= USED 0
sdio_interrupt_flag_clear <= USED 0
sdio_interrupt_flag_get <= USED 0
sdio_operation_disable <= USED 0
sdio_operation_enable <= USED 0
sdio_power_state_get <= USED 0
sdio_power_state_set <= USED 0
sdio_readwait_disable <= USED 0
sdio_readwait_enable <= USED 0
sdio_readwait_type_set <= USED 0
sdio_response_get <= USED 0
sdio_stop_readwait_disable <= USED 0
sdio_stop_readwait_enable <= USED 0
sdio_suspend_disable <= USED 0
sdio_suspend_enable <= USED 0
sdio_wait_type_set <= USED 0
;FILE gd32f4xx_spi.o
__asm___14_gd32f4xx_spi_c_5c0caa7c____REV16 <= USED 0
__asm___14_gd32f4xx_spi_c_5c0caa7c____REVSH <= USED 0
i2s_disable <= USED 0
i2s_enable <= USED 0
i2s_full_duplex_mode_config <= USED 0
i2s_init <= USED 0
i2s_psc_config <= USED 0
qspi_disable <= USED 0
qspi_enable <= USED 0
qspi_io23_output_disable <= USED 0
qspi_io23_output_enable <= USED 0
qspi_read_enable <= USED 0
qspi_write_enable <= USED 0
spi_bidirectional_transfer_config <= USED 0
spi_crc_error_clear <= USED 0
spi_crc_get <= USED 0
spi_crc_next <= USED 0
spi_crc_on <= USED 0
spi_crc_polynomial_get <= USED 0
spi_crc_polynomial_set <= USED 0
spi_disable <= USED 0
spi_dma_disable <= USED 0
spi_dma_enable <= USED 0
spi_i2s_data_frame_format_config <= USED 0
spi_i2s_deinit <= USED 0
spi_i2s_interrupt_disable <= USED 0
spi_i2s_interrupt_enable <= USED 0
spi_i2s_interrupt_flag_get <= USED 0
spi_nss_internal_high <= USED 0
spi_nss_internal_low <= USED 0
spi_nss_output_disable <= USED 0
spi_nss_output_enable <= USED 0
spi_struct_para_init <= USED 0
spi_ti_mode_disable <= USED 0
spi_ti_mode_enable <= USED 0
;FILE gd32f4xx_syscfg.o
__asm___17_gd32f4xx_syscfg_c_e9f420e7____REV16 <= USED 0
__asm___17_gd32f4xx_syscfg_c_e9f420e7____REVSH <= USED 0
syscfg_bootmode_config <= USED 0
syscfg_compensation_config <= USED 0
syscfg_deinit <= USED 0
syscfg_enet_phy_interface_config <= USED 0
syscfg_exmc_swap_config <= USED 0
syscfg_flag_get <= USED 0
syscfg_fmc_swap_config <= USED 0
;FILE gd32f4xx_timer.o
__asm___16_gd32f4xx_timer_c_5b821ed8____REV16 <= USED 0
__asm___16_gd32f4xx_timer_c_5b821ed8____REVSH <= USED 0
timer_auto_reload_shadow_disable <= USED 0
timer_automatic_output_disable <= USED 0
timer_automatic_output_enable <= USED 0
timer_autoreload_value_config <= USED 0
timer_break_config <= USED 0
timer_break_disable <= USED 0
timer_break_enable <= USED 0
timer_break_struct_para_init <= USED 0
timer_channel_capture_value_register_read <= USED 0
timer_channel_complementary_output_polarity_config <= USED 0
timer_channel_complementary_output_state_config <= USED 0
timer_channel_control_shadow_config <= USED 0
timer_channel_control_shadow_update_config <= USED 0
timer_channel_dma_request_source_select <= USED 0
timer_channel_input_capture_prescaler_config <= USED 0
timer_channel_input_struct_para_init <= USED 0
timer_channel_output_clear_config <= USED 0
timer_channel_output_fast_config <= USED 0
timer_channel_output_polarity_config <= USED 0
timer_channel_output_state_config <= USED 0
timer_channel_output_struct_para_init <= USED 0
timer_channel_remap_config <= USED 0
timer_counter_alignment <= USED 0
timer_counter_down_direction <= USED 0
timer_counter_read <= USED 0
timer_counter_up_direction <= USED 0
timer_counter_value_config <= USED 0
timer_dma_disable <= USED 0
timer_dma_enable <= USED 0
timer_dma_transfer_config <= USED 0
timer_event_software_generate <= USED 0
timer_external_clock_mode0_config <= USED 0
timer_external_clock_mode1_config <= USED 0
timer_external_clock_mode1_disable <= USED 0
timer_external_trigger_as_external_clock_config <= USED 0
timer_external_trigger_config <= USED 0
timer_flag_clear <= USED 0
timer_flag_get <= USED 0
timer_hall_mode_config <= USED 0
timer_input_capture_config <= USED 0
timer_input_pwm_capture_config <= USED 0
timer_input_trigger_source_select <= USED 0
timer_internal_clock_config <= USED 0
timer_internal_trigger_as_external_clock_config <= USED 0
timer_interrupt_disable <= USED 0
timer_master_output_trigger_source_select <= USED 0
timer_master_slave_mode_config <= USED 0
timer_output_value_selection_config <= USED 0
timer_prescaler_config <= USED 0
timer_prescaler_read <= USED 0
timer_primary_output_config <= USED 0
timer_quadrature_decoder_mode_config <= USED 0
timer_repetition_value_config <= USED 0
timer_single_pulse_mode_config <= USED 0
timer_slave_mode_select <= USED 0
timer_struct_para_init <= USED 0
timer_update_event_disable <= USED 0
timer_update_event_enable <= USED 0
timer_update_source_config <= USED 0
timer_write_chxval_register_config <= USED 0
;FILE gd32f4xx_tli.o
__asm___14_gd32f4xx_tli_c_99c193f3____REV16 <= USED 0
__asm___14_gd32f4xx_tli_c_99c193f3____REVSH <= USED 0
tli_color_key_disable <= USED 0
tli_color_key_enable <= USED 0
tli_color_key_init <= USED 0
tli_current_pos_get <= USED 0
tli_deinit <= USED 0
tli_disable <= USED 0
tli_dither_config <= USED 0
tli_enable <= USED 0
tli_flag_get <= USED 0
tli_init <= USED 0
tli_interrupt_disable <= USED 0
tli_interrupt_enable <= USED 0
tli_interrupt_flag_clear <= USED 0
tli_interrupt_flag_get <= USED 0
tli_layer_disable <= USED 0
tli_layer_enable <= USED 0
tli_layer_init <= USED 0
tli_layer_struct_para_init <= USED 0
tli_layer_window_offset_modify <= USED 0
tli_line_mark_set <= USED 0
tli_lut_disable <= USED 0
tli_lut_enable <= USED 0
tli_lut_init <= USED 0
tli_lut_struct_para_init <= USED 0
tli_reload_config <= USED 0
tli_struct_para_init <= USED 0
;FILE gd32f4xx_trng.o
__asm___15_gd32f4xx_trng_c_695521eb____REV16 <= USED 0
__asm___15_gd32f4xx_trng_c_695521eb____REVSH <= USED 0
trng_disable <= USED 0
trng_get_true_random_data <= USED 0
trng_interrupt_disable <= USED 0
trng_interrupt_enable <= USED 0
trng_interrupt_flag_clear <= USED 0
trng_interrupt_flag_get <= USED 0
;FILE gd32f4xx_usart.o
__asm___16_gd32f4xx_usart_c_988c0849____REV16 <= USED 0
__asm___16_gd32f4xx_usart_c_988c0849____REVSH <= USED 0
usart_address_config <= USED 0
usart_block_length_config <= USED 0
usart_break_frame_coherence_config <= USED 0
usart_data_first_config <= USED 0
usart_disable <= USED 0
usart_dma_receive_config <= USED 0
usart_dma_transmit_config <= USED 0
usart_flag_clear <= USED 0
usart_guard_time_config <= USED 0
usart_halfduplex_disable <= USED 0
usart_halfduplex_enable <= USED 0
usart_hardware_flow_coherence_config <= USED 0
usart_hardware_flow_cts_config <= USED 0
usart_hardware_flow_rts_config <= USED 0
usart_interrupt_flag_clear <= USED 0
usart_invert_config <= USED 0
usart_irda_lowpower_config <= USED 0
usart_irda_mode_disable <= USED 0
usart_irda_mode_enable <= USED 0
usart_lin_break_detection_length_config <= USED 0
usart_lin_mode_disable <= USED 0
usart_lin_mode_enable <= USED 0
usart_mute_mode_disable <= USED 0
usart_mute_mode_enable <= USED 0
usart_mute_mode_wakeup_config <= USED 0
usart_oversample_config <= USED 0
usart_parity_check_coherence_config <= USED 0
usart_parity_config <= USED 0
usart_prescaler_config <= USED 0
usart_receiver_timeout_disable <= USED 0
usart_receiver_timeout_enable <= USED 0
usart_receiver_timeout_threshold_config <= USED 0
usart_sample_bit_config <= USED 0
usart_send_break <= USED 0
usart_smartcard_autoretry_config <= USED 0
usart_smartcard_mode_disable <= USED 0
usart_smartcard_mode_enable <= USED 0
usart_smartcard_mode_nack_disable <= USED 0
usart_smartcard_mode_nack_enable <= USED 0
usart_stop_bit_set <= USED 0
usart_synchronous_clock_config <= USED 0
usart_synchronous_clock_disable <= USED 0
usart_synchronous_clock_enable <= USED 0
usart_word_length_set <= USED 0
;FILE gd32f4xx_wwdgt.o
__asm___16_gd32f4xx_wwdgt_c_c410292e____REV16 <= USED 0
__asm___16_gd32f4xx_wwdgt_c_c410292e____REVSH <= USED 0
wwdgt_config <= USED 0
wwdgt_counter_update <= USED 0
wwdgt_deinit <= USED 0
wwdgt_enable <= USED 0
wwdgt_flag_clear <= USED 0
wwdgt_flag_get <= USED 0
wwdgt_interrupt_enable <= USED 0
;FILE gdwatch.o
__asm___9_gdwatch_c_46dc3388____REV16 <= USED 0
__asm___9_gdwatch_c_46dc3388____REVSH <= USED 0
;FILE gnss.o
GNSS_Buff_Parser <= USED 0
GNSS_Cmd_Kind_Parser <= USED 0
GNSS_Cmd_Parser <= USED 0
__asm___6_gnss_c_878de2bc____REV16 <= USED 0
__asm___6_gnss_c_878de2bc____REVSH <= USED 0
gnss_AGRIC_Buff_Parser <= USED 0
gnss_BESTPOS_Buff_Parser <= USED 0
gnss_BESTVEL_Buff_Parser <= USED 0
gnss_Fetch_Data <= USED 0
gnss_GGA_Buff_Parser <= USED 0
gnss_Heading_Buff_Parser <= USED 0
gnss_RMC_Buff_Parser <= USED 0
gnss_TRA_Buff_Parser <= USED 0
gnss_VTG_Buff_Parser <= USED 0
gnss_ZDA_Buff_Parser <= USED 0
gnss_config_movingbase <= USED 0
gnss_dataIsOk <= USED 0
gnss_fill_data <= USED 0
gnss_get_algorithm_dataPtr <= USED 0
gnss_get_baseline <= USED 0
gnss_gprmcIsLocation <= USED 0
gnss_isLocation <= USED 0
gnss_parse <= USED 0
gnss_request_GGA <= USED 0
gnss_request_GSA <= USED 0
gnss_request_HDT <= USED 0
gnss_request_HEADING <= USED 0
gnss_request_OTG <= USED 0
gnss_request_RMC <= USED 0
gnss_request_ZDA <= USED 0
gnss_request_saveconfig <= USED 0
gnss_set_ins_offset <= USED 0
gnss_set_leverArm <= USED 0
gnss_set_posture <= USED 0
gnss_timeWrToArm2 <= USED 0
gnss_time_is_valid <= USED 0
strtok_test <= USED 0
;FILE imu_data.o
__asm___10_imu_data_c____REV16 <= USED 0
__asm___10_imu_data_c____REVSH <= USED 0
;FILE ins_data.o
__asm___10_INS_Data_c_92692a63____REV16 <= USED 0
__asm___10_INS_Data_c_92692a63____REVSH <= USED 0
read_flash <= USED 0
save_flash <= USED 0
;FILE ins_output.o
__asm___12_INS_Output_c_syncount____REV16 <= USED 0
__asm___12_INS_Output_c_syncount____REVSH <= USED 0
;FILE ins_sys.o
__asm___9_INS_Sys_c____REV16 <= USED 0
__asm___9_INS_Sys_c____REVSH <= USED 0
;FILE logger.o
CopyAndConvertFile <= USED 0
SDFile2USB <= USED 0
__asm___8_Logger_c_ec17e47c____REV16 <= USED 0
__asm___8_Logger_c_ec17e47c____REVSH <= USED 0
;FILE main.o
GetChipID <= USED 0
SetDefaultProductInfo <= USED 0
StartNavigation <= USED 0
StopNavigation <= USED 0
__asm___6_main_c_705613f2____REV16 <= USED 0
__asm___6_main_c_705613f2____REVSH <= USED 0
checkUSBReady <= USED 0
loggingLogFile <= USED 0
;FILE nav.o
SendOBSGNSSData <= USED 0
SendOBSIMUData <= USED 0
SendSINSData <= USED 0
SetNavFunsionSource <= USED 0
__asm___5_nav_c_6c5f807b____REV16 <= USED 0
__asm___5_nav_c_6c5f807b____REVSH <= USED 0
;FILE nav_app.o
KinematicEstimation <= USED 0
NAV_function_LD_TEST <= USED 0
NAV_function_UAV <= USED 0
NavDRMode <= USED 0
__asm___9_nav_app_c_879d0329____REV16 <= USED 0
__asm___9_nav_app_c_879d0329____REVSH <= USED 0
;FILE nav_cli.o
__asm___9_nav_cli_c_d813a119____REV16 <= USED 0
__asm___9_nav_cli_c_d813a119____REVSH <= USED 0
;FILE nav_gnss.o
GNSS_init <= USED 0
__asm___10_nav_gnss_c_1bab0f07____REV16 <= USED 0
__asm___10_nav_gnss_c_1bab0f07____REVSH <= USED 0
gps_data_check <= USED 0
;FILE nav_imu.o
InitZeroOffsetCompenstation <= USED 0
__asm___9_nav_imu_c_390ddb1e____REV16 <= USED 0
__asm___9_nav_imu_c_390ddb1e____REVSH <= USED 0
install_data_check <= USED 0
nav_calib <= USED 0
savebuff <= USED 0
;FILE nav_kf.o
CalDiffPosVelINS2RTK <= USED 0
CalGPSTimeCompensate <= USED 0
CalcAngleAntAndIMU <= USED 0
CalcCa2n <= USED 0
CheckFusionStatusAndTime <= USED 0
Est_Wheel_delay <= USED 0
Q_MAT_UP <= USED 0
SetGPSFunsionPolicy <= USED 0
TEST_UP <= USED 0
__asm___8_nav_kf_c_f7e861e7____REV16 <= USED 0
__asm___8_nav_kf_c_f7e861e7____REVSH <= USED 0
;FILE nav_magnet.o
CalMagDeclination <= USED 0
Get_Magnet_Data <= USED 0
ProMagData <= USED 0
__asm___12_nav_magnet_c_bb94d727____REV16 <= USED 0
__asm___12_nav_magnet_c_bb94d727____REVSH <= USED 0
;FILE nav_mahony.o
IIRFilter <= USED 0
MahonyInit <= USED 0
MahonyUpdate <= USED 0
MahonyUpdate_NoMAG <= USED 0
SetPidParmByMotion <= USED 0
__asm___12_nav_mahony_c_4c6970fd____REV16 <= USED 0
__asm___12_nav_mahony_c_4c6970fd____REVSH <= USED 0
interplim <= USED 0
;FILE nav_math.o
ABS <= USED 0
BDAddFieldDouble <= USED 0
BDAddFieldFloat <= USED 0
BDAddFieldInt16 <= USED 0
BDAddFieldInt32 <= USED 0
BDAddFieldUInt32 <= USED 0
BDAddFieldi4 <= USED 0
BDAddFieldu2 <= USED 0
CalculateCheckSum <= USED 0
Cnb2att <= USED 0
GetDoubleParmsFormString <= USED 0
GetFracFromDouble <= USED 0
WRAP_PI <= USED 0
__asm___10_nav_math_c_2e76c2c8____REV16 <= USED 0
__asm___10_nav_math_c_2e76c2c8____REVSH <= USED 0
covecef <= USED 0
covenu <= USED 0
dot <= USED 0
ecef2enu <= USED 0
ecef2pos <= USED 0
enu2ecef <= USED 0
eyes <= USED 0
get_D64 <= USED 0
get_F32 <= USED 0
get_Int32 <= USED 0
get_UInt32 <= USED 0
get_Ushort <= USED 0
lubksb <= USED 0
ludcmp <= USED 0
m2qnb <= USED 0
matcpy <= USED 0
matinv <= USED 0
normv3 <= USED 0
pos2ecef <= USED 0
qConj <= USED 0
qmulv <= USED 0
rotv <= USED 0
rv2m <= USED 0
xyz2enu <= USED 0
;FILE nav_ods.o
__asm___9_nav_ods_c_P_Cvb____REV16 <= USED 0
__asm___9_nav_ods_c_P_Cvb____REVSH <= USED 0
;FILE nav_sins.o
CalEarthGn <= USED 0
Cnscl_1 <= USED 0
Lever <= USED 0
SINS_UP <= USED 0
SINS_UP_ATT <= USED 0
SINS_UP_DR <= USED 0
SINS_UP_HP <= USED 0
SINS_UP_VEL <= USED 0
__asm___10_nav_sins_c_3fb512db____REV16 <= USED 0
__asm___10_nav_sins_c_3fb512db____REVSH <= USED 0
;FILE nav_uwb.o
Get_UWB_Data <= USED 0
UWB_init <= USED 0
__asm___9_nav_uwb_c_UWB_init____REV16 <= USED 0
__asm___9_nav_uwb_c_UWB_init____REVSH <= USED 0
;FILE navlog.o
__asm___8_navlog_c_12832b5b____REV16 <= USED 0
__asm___8_navlog_c_12832b5b____REVSH <= USED 0
inav_datetime_get <= USED 0
inav_get_loglevel <= USED 0
inav_log <= USED 0
inav_set_loglevel <= USED 0
;FILE protocol.o
HexToAscii <= USED 0
__asm___10_protocol_c_155649fd____REV16 <= USED 0
__asm___10_protocol_c_155649fd____REVSH <= USED 0
iPMV_GIMU_BIN_Send <= USED 0
iPMV_GINSSTD_BIN_Send <= USED 0
iPMV_GINS_BIN_Send <= USED 0
iPMV_protocol_report <= USED 0
protocol_crc32_init <= USED 0
protocol_fillGipotData <= USED 0
protocol_fillGrimuData <= USED 0
protocol_fillRawimuData <= USED 0
protocol_gnggaDataGet <= USED 0
protocol_gnrmcDataGet <= USED 0
protocol_report <= USED 0
;FILE segger_rtt.o
SEGGER_RTT_AllocDownBuffer <= USED 0
SEGGER_RTT_AllocUpBuffer <= USED 0
SEGGER_RTT_ConfigDownBuffer <= USED 0
SEGGER_RTT_ConfigUpBuffer <= USED 0
SEGGER_RTT_GetAvailWriteSpace <= USED 0
SEGGER_RTT_GetBytesInBuffer <= USED 0
SEGGER_RTT_GetKey <= USED 0
SEGGER_RTT_HasData <= USED 0
SEGGER_RTT_HasDataUp <= USED 0
SEGGER_RTT_HasKey <= USED 0
SEGGER_RTT_Init <= USED 0
SEGGER_RTT_PutChar <= USED 0
SEGGER_RTT_PutCharSkip <= USED 0
SEGGER_RTT_PutCharSkipNoLock <= USED 0
SEGGER_RTT_Read <= USED 0
SEGGER_RTT_ReadNoLock <= USED 0
SEGGER_RTT_ReadUpBuffer <= USED 0
SEGGER_RTT_ReadUpBufferNoLock <= USED 0
SEGGER_RTT_SetFlagsDownBuffer <= USED 0
SEGGER_RTT_SetFlagsUpBuffer <= USED 0
SEGGER_RTT_SetNameDownBuffer <= USED 0
SEGGER_RTT_SetNameUpBuffer <= USED 0
SEGGER_RTT_SetTerminal <= USED 0
SEGGER_RTT_TerminalOut <= USED 0
SEGGER_RTT_WaitKey <= USED 0
SEGGER_RTT_Write <= USED 0
SEGGER_RTT_WriteDownBuffer <= USED 0
SEGGER_RTT_WriteDownBufferNoLock <= USED 0
SEGGER_RTT_WriteNoLock <= USED 0
SEGGER_RTT_WriteSkipNoLock <= USED 0
SEGGER_RTT_WriteString <= USED 0
SEGGER_RTT_WriteWithOverwriteNoLock <= USED 0
;FILE segger_rtt_printf.o
SEGGER_RTT_printf <= USED 0
SEGGER_RTT_vprintf <= USED 0
;FILE startup_gd32f450_470.o
;FILE system_gd32f4xx.o
SystemCoreClockUpdate <= USED 0
__asm___17_system_gd32f4xx_c_5d646a67____REV16 <= USED 0
__asm___17_system_gd32f4xx_c_5d646a67____REVSH <= USED 0
;FILE systick.o
__asm___9_systick_c_12b3d2f7____REV16 <= USED 0
__asm___9_systick_c_12b3d2f7____REVSH <= USED 0
delay_xms <= USED 0
systick_config <= USED 0
;FILE tcpserver.o
CH395GlobalInterrupt <= USED 0
CH395SocketInitOpen <= USED 0
CH395SocketInterrupt <= USED 0
InitSocketParam <= USED 0
PHY_IsConnect <= USED 0
__asm___11_TCPServer_c_7dad0641____REV16 <= USED 0
__asm___11_TCPServer_c_7dad0641____REVSH <= USED 0
;FILE time_unify.o
Bdt2UtcTime <= USED 0
__asm___12_Time_Unify_c_gpst0____REV16 <= USED 0
__asm___12_Time_Unify_c_gpst0____REVSH <= USED 0
bdt2gpst <= USED 0
bdt2time <= USED 0
gpst2bdt <= USED 0
str2time <= USED 0
time2bdt <= USED 0
time2gpst <= USED 0
time2gst <= USED 0
time2sec <= USED 0
utc2gmst <= USED 0
utc2gpst <= USED 0
;FILE transplant.o
__asm___12_transplant_c_1e6705d7____REV16 <= USED 0
__asm___12_transplant_c_1e6705d7____REVSH <= USED 0
calibAccel <= USED 0
calibGyro <= USED 0
;FILE ymodem.o
__asm___8_ymodem_c_CRC16____REV16 <= USED 0
__asm___8_ymodem_c_CRC16____REVSH <= USED 0
