--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol
-I.\RTE\_INS_4000
-I"D:\Program Files\MDK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"
-I"D:\Program Files\MDK\ARM\CMSIS\Include"
-D__UVISION_VERSION="528" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS
-o .\objects\systick.o --omf_browse .\objects\systick.crf --depend .\objects\systick.d --feedback ".\Objects\arm2.fed" "..\Source\src\systick.c"