/********************************** (C) COPYRIGHT *******************************
* File Name          : SPI_SW.C
* Author             : MJX
* Version            : V1.20
* Date               : 2015/08/28
* Description        : CH378芯片 软件模拟SPI串行连接的硬件抽象层 V1.0
*                      提供I/O接口子程序
*******************************************************************************/



/******************************************************************************/
/* 头文件包含 */
#include "stdio.h"
#include "systick.h"
#include "CH378INC.H"						 				 /* CH378定义相关头文件 */
#include "CH378_HAL.h"


/*******************************************************************************
* Function Name  : CH378_PORT_INIT
* Description    : CH378端口初始化
*                  由于使用软件模拟SPI读写时序,所以进行初始化
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void CH378_Port_Init( void )
{
#if CHIP_USED == USE_CHIP_GD32
	
	/* 初始化SPI接口 */
	rcu_periph_clock_enable(RCU_GPIOG);
	rcu_periph_clock_enable(RCU_GPIOA);

	// SPI SCK/CS/MOSI pin configuration
	gpio_mode_set(CH378_SPI_Port, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLUP, CH378_SPI_CS_PIN | CH378_SPI_MOSI_PIN | CH378_SPI_CLK_PIN);
	gpio_output_options_set(CH378_SPI_Port, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, CH378_SPI_CS_PIN | CH378_SPI_MOSI_PIN | CH378_SPI_CLK_PIN);
	
	// MISO INT Pin
	gpio_mode_set(CH378_SPI_Port, GPIO_MODE_INPUT, GPIO_PUPD_PULLUP, CH378_SPI_MISO_PIN);
	gpio_mode_set(CH378_INT_Port, GPIO_MODE_INPUT, GPIO_PUPD_PULLUP, CH378_INT_PIN);
	/* 设置引脚SCS(PB0)、SCK(PA5)、MOSI(PA7)默认输出电平 */
	CH378_SPI_SCS_HIGH( );
	CH378_SPI_SCK_HIGH( );
	CH378_SPI_SDI_HIGH( );
	
	
#else
	GPIO_InitTypeDef GPIO_InitStructure;

	/* 配置SPI接口对应的引脚SCS(PB0)、SCK(PA5)、MOSI(PA7)方向均为输出 */
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_5 | GPIO_Pin_7;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;			/* 推挽式输出 */
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init( GPIOA, &GPIO_InitStructure );

	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_0;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;			/* 推挽式输出 */
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init( GPIOB, &GPIO_InitStructure );

	/* 配置MISO(PA6)、INT#(PA1)方向均为输入 */
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_1 | GPIO_Pin_6;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;				/* 上拉输入 */
	GPIO_Init( GPIOA, &GPIO_InitStructure );

	/* 设置引脚SCS(PB0)、SCK(PA5)、MOSI(PA7)默认输出电平 */
	CH378_SPI_SCS_HIGH( );
	CH378_SPI_SCK_HIGH( );
	CH378_SPI_SDI_HIGH( );
#endif
}

/*******************************************************************************
* Function Name  : xWriteCH378Cmd
* Description    : 向CH378写命令
* Input          : mCmd---将要写入CH378的命令码
* Output         : None
* Return         : None
*******************************************************************************/
void xWriteCH378Cmd( UINT8 mCmd ) 
{
#if CHIP_USED == USE_CHIP_GD32

	UINT8  i;

	CH378_SPI_SCS_HIGH( );  									 /* 防止之前未通过xEndCH378Cmd禁止SPI片选 */

	/* 对于双向I/O引脚模拟SPI接口,那么必须确保已经设置SPI_SCS,SPI_SCK,SPI_SDI为输出方向,SPI_SDO为输入方向 */
	i = 8;
	CH378_SPI_SCS_LOW( ); 										 /* SPI片选有效 */
	while( i-- )
	{
		CH378_SPI_SCK_LOW( );
		
		if( mCmd & 0x80 ) 
		{
			CH378_SPI_SDI_HIGH( );
		}
		else 
		{
			CH378_SPI_SDI_LOW( );
		}
		mCmd <<= 1;  											 /* 数据位是高位在前 */
		CH378_SPI_SCK_HIGH( );  								 /* CH378在时钟上升沿采样输入 */
	}
	CH378_mDelayuS( 2 );  											 /* 延时2uS确保读写周期大于2uS,或者用上面一行的状态查询代替 */
	
#else
	UINT8  i;

	CH378_SPI_SCS_HIGH( );  									 /* 防止之前未通过xEndCH378Cmd禁止SPI片选 */

	/* 对于双向I/O引脚模拟SPI接口,那么必须确保已经设置SPI_SCS,SPI_SCK,SPI_SDI为输出方向,SPI_SDO为输入方向 */
	i = 8;
	CH378_SPI_SCS_LOW( ); 										 /* SPI片选有效 */
	while( i-- )
	{
		CH378_SPI_SCK_LOW( );
		if( mCmd & 0x80 ) 
		{
			CH378_SPI_SDI_HIGH( );
		}
		else 
		{
			CH378_SPI_SDI_LOW( );
		}
		mCmd <<= 1;  											 /* 数据位是高位在前 */
		CH378_SPI_SCK_HIGH( );  								 /* CH378在时钟上升沿采样输入 */
	}
	mDelayuS( 2 );  											 /* 延时2uS确保读写周期大于2uS,或者用上面一行的状态查询代替 */
#endif
}

/*******************************************************************************
* Function Name  : xWriteCH378Data
* Description    : 向CH378写数据
* Input          : mData---将要写入CH378的数据
* Output         : None
* Return         : None
*******************************************************************************/
void xWriteCH378Data( UINT8 mData )  
{
#if CHIP_USED == USE_CHIP_GD32
	UINT8  i;

	i = 8;
	while( i-- )
	{
		CH378_SPI_SCK_LOW( );
		
		if( mData & 0x80 ) 
		{
			CH378_SPI_SDI_HIGH( );
		}
		else 
		{
			CH378_SPI_SDI_LOW( );
		}
		mData <<= 1;  											 /* 数据位是高位在前 */
		CH378_SPI_SCK_HIGH( );  								 /* CH378在时钟上升沿采样输入 */
	}
#else
	UINT8  i;

	i = 8;
	while( i-- )
	{
		CH378_SPI_SCK_LOW( );
		if( mData & 0x80 ) 
		{
			CH378_SPI_SDI_HIGH( );
		}
		else 
		{
			CH378_SPI_SDI_LOW( );
		}
		mData <<= 1;  											 /* 数据位是高位在前 */
		CH378_SPI_SCK_HIGH( );  								 /* CH378在时钟上升沿采样输入 */
	}
#endif
}



/*******************************************************************************
* Function Name  : xReadCH378Data
* Description    : 从CH378读数据
* Input          : None
* Output         : None
* Return         : 返回读取的数据
*******************************************************************************/
UINT8 xReadCH378Data( void )
{
#if CHIP_USED == USE_CHIP_GD32
	UINT8  i, d;
	
	d = 0;
	i = 8;
	while( i-- )
	{
		CH378_SPI_SCK_LOW( );							 		 /* CH378在时钟下降沿输出 */
		CH378_SPI_SCK_LOW( );
		
		d <<= 1;  												 /* 数据位是高位在前 */
		if( CH378_SPI_SDO_PIN() != 0x00 )
		{
			d++;
		}
		CH378_SPI_SCK_HIGH( );
	}
	return( d );
#else
	/* 如果是硬件SPI接口,应该是先查询SPI状态寄存器以等待SPI字节传输完成,然后从SPI数据寄存器读出数据 */
	UINT8  i, d;
	
	d = 0;
	i = 8;
	while( i-- )
   	{
		CH378_SPI_SCK_LOW( );							 		 /* CH378在时钟下降沿输出 */
		CH378_SPI_SCK_LOW( );
		d <<= 1;  												 /* 数据位是高位在前 */
		if( ( GPIOA->IDR & GPIO_Pin_6 ) != 0x00 ) 
		{
			d++;
		}
		CH378_SPI_SCK_HIGH( );
	}
	return( d );
#endif
}



/*******************************************************************************
* Function Name  : Query378Interrupt
* Description    : 查询CH378中断(INT#低电平)
* Input          : None
* Output         : None
* Return         : 返回中断状态
*******************************************************************************/
UINT8 Query378Interrupt( void )
{
	/* 如果连接了CH378的中断引脚则直接查询中断引脚 */
	/* 如果未连接CH378的中断引脚则查询兼做中断输出的SDO引脚状态 */
#ifdef	CH378_INT_WIRE
	return( CH378_INT_PIN_WIRE( ) ? FALSE : TRUE );  
#else
	return( CH378_SPI_SDO_PIN( ) ? FALSE : TRUE );  
#endif
}

/*******************************************************************************
* Function Name  : mInitCH378Host
* Description    : 初始化CH378
* Input          : None
* Output         : None
* Return         : 返回操作状态
*******************************************************************************/
UINT8 mInitCH378Host( void ) 
{
	UINT8  res, i;

	/* 检测CH378连接是否正常 */
	CH378_mDelaymS(200);
	CH378_mDelaymS(200);
	CH378_mDelaymS(200);
	CH378_Port_Init( );							/* 接口硬件初始化 */
	
	xWriteCH378Cmd( CMD11_CHECK_EXIST );		/* 测试单片机与CH378之间的通讯接口 */
	xWriteCH378Data( 0xAA );
	delay_us(1000);
	res = xReadCH378Data( );
	xEndCH378Cmd( );
	
	if( res != 0x55 )	return( ERR_USB_UNKNOWN );
	/* 通讯接口不正常,可能原因有:接口连接异常,其它设备影响(片选不唯一),串口波特率,一直在复位,晶振不工作 */
	for( i = 0; i < 100; i++)
	{
		/* 设置CH378工作模式 */
		xWriteCH378Cmd( CMD11_SET_USB_MODE );		/* 设备USB工作模式 */
		xWriteCH378Data( 0x04 );					/* 操作SD卡 */
//		xWriteCH378Data( 0x07 );					/* 操作USB存储设备 */
		/* 等待模式设置完毕,对于操作SD卡大概需要10mS左右时间,对于操作USB设备大概需要35mS左右时间 */
		CH378_mDelaymS( 50 );
		res = xReadCH378Data( );
		xEndCH378Cmd( );
#ifndef	CH378_INT_WIRE
		xWriteCH378Cmd( CMD20_SET_SDO_INT );		/* 设置SPI的SDO引脚的中断方式 */
		xWriteCH378Data( 0x16 );
		xWriteCH378Data( 0x01 );					/* SDO引脚在SCS片选无效时兼做中断请求输出 */
		xEndCH378Cmd( );
#endif
		if( res == CMD_RET_SUCCESS ) return( ERR_SUCCESS );
	}
	return( ERR_USB_UNKNOWN );				/* 设置模式错误 */
}
