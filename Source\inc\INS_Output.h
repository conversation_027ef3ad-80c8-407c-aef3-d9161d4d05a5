/*!
    \file  main.h
    \brief the header file of main 
*/

/*
    Copyright (C) 2016 GigaDevice

    2016-08-15, V1.0.0, firmware for GD32F4xx
*/

#ifndef __INS_OUTPUT_H
#define __INS_OUTPUT_H
#include "gd32f4xx_can.h"
#include "frame_analysis.h"

#pragma pack(1)
typedef struct _opticalgyroprotocol {
	unsigned char head0;
	unsigned char head1;
	unsigned char data[6];
	unsigned char xord;
} opticalgyroprotocol_t;
#pragma pack()

// 函数声明
void NAV_Output(void);

#endif /* __INS_OUTPUT_H */


