/*!
    \file  fpga.c
    \brief
*/


#include "fpgad.h"


fpgadata_t	gpagedata;

// UART4相关变量（这些变量在gd32f4xx_it.c中已定义，这里只是注释说明）
// unsigned char grxbuffer[U4RX_MAXCOUNT];
// int grxlen = 0, grxst = 0;
unsigned char gframeParsebuf[FRAMEPARSEBUFSIZE];

// UART6相关变量（这些变量在gd32f4xx_it.c中已定义，这里只是注释说明）
// unsigned char grxbuffer6[U4RX_MAXCOUNT];
// int grxlen6 = 0, grxst6 = 0;
// u8 g_Uart6_Rx_Finish = 0;
// int gbtxcompleted6 = 1;
// int nbr_data_to_send6 = 0;
// unsigned char tx_buffer6[64];
















