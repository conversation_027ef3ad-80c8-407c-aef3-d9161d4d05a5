//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：FirmwareUpdateFile.c
// 文件标识：
// 文件摘要：
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2024.10.09
//---------------------------------------------------------
#include "stdio.h"
#include "stdlib.h"
#include "string.h"
#include "appmain.h"

#include "core_cm4.h"
//#include "types.h"
#include "fpgad.h"



#include "FirmwareUpdateFile.h"

Update_Rx_FirmWare  UpdateFirmWareRx;	//接收，上位机发送的数据包
Update_Tx_FirmWare	UpdateFirmWareTx;	//发送，下位机发送给上位机信息
u8 g_StartUpdateFirm=0;//开始更新标志 1:开始更新 0:不更新


u8 UartTxBuf[64]={0};

//擦除扇区
void  Drv_FlashErase(u32 address,bool flgErase)
{
    fmc_sector_info_struct sector_info;
    /* get information about the sector in which the specified address is located */
    sector_info = fmc_sector_info_get(address);
    if(FMC_WRONG_SECTOR_NAME == sector_info.sector_name) {

        while(1);
    } else {

        /* unlock the flash program erase controller */
		if (flgErase == TRUE)
        	fmc_unlock();
        /* clear pending flags */
        fmc_flag_clear(FMC_FLAG_END | FMC_FLAG_OPERR | FMC_FLAG_WPERR | FMC_FLAG_PGMERR | FMC_FLAG_PGSERR);
        /* wait the erase operation complete*/
        if(FMC_READY != fmc_sector_erase(sector_info.sector_num)) {
            while(1);
        }
        /* lock the flash program erase controller */
		if (flgErase == TRUE)
        	fmc_lock();
    }
}




//写入FLASH
void Drv_FlashWrite(u8 *pucBuff, u32 uiAddress, u32 uiLen, bool flgErase)
{
	fmc_state_enum fmc_state;
	u32 uiIndex = 0;
	u32 uiData = 0;
	u32 j = 0;

	//__disable_irq();
	//__disable_fault_irq();
	fmc_unlock();
	fmc_flag_clear(FMC_FLAG_END | FMC_FLAG_OPERR | FMC_FLAG_WPERR | FMC_FLAG_PGMERR | FMC_FLAG_PGSERR);
	if (flgErase == TRUE)
	{
		Drv_FlashErase(uiAddress,FALSE);
		//delay_ms(1000);
		//if (FMC_READY != fmc_state)
		{
			//fmc_lock();
			//return;
		}
	}

	//将单字节拼接成4字节写入
	for (uiIndex = 0; uiIndex < uiLen; uiIndex += 4)
	{
		uiData = 0;
		for (j = 0; j < 4; j++)
		{
			uiData = uiData >> 8;
			if (uiIndex + j < uiLen)
			{
				uiData |= ((u32)pucBuff[uiIndex + j] << 24);
			}
		}
		fmc_state = fmc_word_program(uiAddress + uiIndex, uiData);
		if (fmc_state != FMC_READY)
		{
			break;
		}
	}

	/*for(uiIndex=0; uiIndex<uiLen; uiIndex++)
	{
        if(FMC_READY == fmc_word_program(uiAddress, pucBuff[uiIndex]))
		{
            uiAddress = uiAddress + 4;
        } else
        {
            while(1);
        }
    }*/

	fmc_lock();
	//__enable_fault_irq();
	//__enable_irq();
}

void Drv_FlashWrite1(u32* data_32,u32 address, u32 length, bool flgErase)
{
//    fmc_sector_info_struct start_sector_info;
//    fmc_sector_info_struct end_sector_info;
//    uint32_t sector_num;
		u32 i;
    /* unlock the flash program erase controller */
    fmc_unlock();
    /* clear pending flags */
    fmc_flag_clear(FMC_FLAG_END | FMC_FLAG_OPERR | FMC_FLAG_WPERR | FMC_FLAG_PGMERR | FMC_FLAG_PGSERR);
	if (flgErase == TRUE)
	{
		Drv_FlashErase(address,FALSE);
	}
#if 0
    /* get the information of the start and end sectors */
    start_sector_info = fmc_sector_info_get(address);
    end_sector_info = fmc_sector_info_get(address + 4*length);
    /* erase sector */
    for(i = start_sector_info.sector_name; i <= end_sector_info.sector_name; i++) {
        sector_num = sector_name_to_number(i);
        if(FMC_READY != fmc_sector_erase(sector_num)) {
            while(1);
        }
    }
#endif
    /* write data_32 to the corresponding address */
    for(i=0; i<length; i++) {
        if(FMC_READY == fmc_word_program(address, data_32[i])) {
            address = address + 4;
        } else {
            while(1);
        }
    }
    /* lock the flash program erase controller */
    fmc_lock();

}



//读取FLASH
void Drv_FlashRead(u32 *pucBuff, u32 uiAddress, u32 uiLen)
{
	u8 i;

	for(i=0; i<uiLen; i++)
	{
        pucBuff[i] = *(volatile u32*)uiAddress;
        uiAddress=uiAddress + 4;
    }
}

void Drv_FlashRead_8bit(u8* data_8,u32 address, u32 length)
{
    u32 i;

    for(i=0; i<length; i++) {
        data_8[i] = *(__IO int8_t*)address;
        address++;
    }

}


//单字节写入FLASH
void Drv_FlashWrite_8bit(u32 address, u32 length, u8* data_8)
{
	fmc_sector_info_struct start_sector_info;
	fmc_sector_info_struct end_sector_info;
	uint32_t sector_num,i;

	fmc_unlock();

	fmc_flag_clear(FMC_FLAG_END | FMC_FLAG_OPERR | FMC_FLAG_WPERR | FMC_FLAG_PGMERR | FMC_FLAG_PGSERR);

	/* get the information of the start and end sectors */
	/*start_sector_info = fmc_sector_info_get(address);
	end_sector_info = fmc_sector_info_get(address + 2*length);
	//擦除扇区
	for(i = start_sector_info.sector_name; i <= end_sector_info.sector_name; i++){
		sector_num = sector_name_to_number(i);
		if(FMC_READY != fmc_sector_erase(sector_num)){
			while(1);
		}
	}*/

	for(i=0; i<length; i++){
		if(FMC_READY == fmc_byte_program(address, data_8[i])){
			address++;
		}else{
			while(1);
		}
	}

	fmc_lock();
}


//MCU系统重启
void Drv_SystemReset(void)
{
	NVIC_SystemReset();
}


void UartWrite(u32 UartId,char *data,int len)
{
	int t=0;

 	for(t=0;t<len;t++)
	{
		 if(RESET != usart_interrupt_flag_get(UartId, USART_INT_FLAG_TBE))
		 {
		 	usart_data_transmit(UartId, data[t]);
		 }

		//while(usart_flag_get(UartId,USART_FLAG_TC)!=SET);
 	}

}


//校验和
u8 CheckSum(Update_Rx_FirmWare FirmData)
{
	u32 InfoData = 0,UpdateData=0,reserve=0;
	u8  i=0,ucSum=0;

	for(i=0;i<40;i++)
	{
		UpdateData += FirmData.info.ucUpdateData[i];
	}

	for(i=0;i<6;i++)
	{
		reserve += FirmData.info.reserve[i];
	}

	InfoData = FirmData.info.ucUpdateType + FirmData.info.ucLength + ((u8)(FirmData.info.usBaoIndex)+(u8)(FirmData.info.usBaoIndex>>8)) + ((u8)(FirmData.info.usTotalBao)+(u8)(FirmData.info.usTotalBao>>8)) + UpdateData + reserve;

	ucSum = (u8)(((u8)(FirmData.head.cmd)+(u8)(FirmData.head.cmd>>8)) + InfoData + ((u8)(FirmData.ender.count)+(u8)(FirmData.ender.count>>8)));

	return ucSum;
}



//CRC校验
u8 Get_CRC8(u8 *ptr, u8 len)
{
	u8 i,j;
	u8 crc = 0x00;

	for(i = 0; i < len; i++)
	{
		crc ^= ptr[i];
		for (j = 0; j < 8; j++)
		{
			if (crc & 0x80)
				crc = (crc << 1) ^ 0x31;
			else
				crc = (crc << 1);
		}
	}

	return (crc);
}

u8 crc_verify_8bit(u8* srcbuf,u32 srcqty)
{
	u32 tmpvlu = 0;
	for(u32 i = 0; i < srcqty; i++)
	{
		tmpvlu += srcbuf[i];
	}
	return (u8)tmpvlu;
}






//文件处理
void UpdateFileHandle(u8 *pucBuf,u16 usIndex,u16 usTotalBao,u8 ucLen)
{
	static u32 uiOffsetAddr=0,uiOffsetAddr1=0,uiLastBaoInDex=1,flag=0;
	u8 UpdateFlagBuff[5]={0};//更新程序完成标志,程序完成后，保存此标志到FLASH指定位置，下次程序启动后，此标志清零

	if(uiLastBaoInDex == usIndex)
	{
		return;
	}

	if(flag==0)
	{
		flag=1;
		if(0 !=usIndex)
		{
			UpdateFirmWareTx.info.ucRxFlag=2;//数据异常，下位机重发
			return;
		}
	}

#if 0
	if(usIndex == 0)
	{
		uiOffsetAddr=0;
		Drv_FlashWrite(pucBuf, APP_UPDATE_ADDRESS+uiOffsetAddr, ucLen, TRUE);
		uiOffsetAddr += ucLen;
	}
	else if(usIndex<4096)
	{
		Drv_FlashWrite(pucBuf, APP_UPDATE_ADDRESS+uiOffsetAddr, ucLen, FALSE);
		uiOffsetAddr += ucLen;
	}
	else if(usIndex == 4096)
	{
		uiOffsetAddr1=0;
		Drv_FlashWrite(pucBuf, APP_UPDATE_ADDRESS1+uiOffsetAddr1, ucLen, TRUE);
		uiOffsetAddr1 += ucLen;
	}
	else
	{
		Drv_FlashWrite(pucBuf, APP_UPDATE_ADDRESS1+uiOffsetAddr1, ucLen, FALSE);
		uiOffsetAddr1 += ucLen;
	}
#else
	if(usIndex == 0)
	{
		uiOffsetAddr=0;
		Drv_FlashErase(APP_UPDATE_ADDRESS,TRUE);
		Drv_FlashErase(APP_UPDATE_ADDRESS1,TRUE);
		Drv_FlashWrite(pucBuf, APP_UPDATE_ADDRESS+uiOffsetAddr, ucLen, FALSE);
		uiOffsetAddr += ucLen;
	}
	else
	{
		Drv_FlashWrite(pucBuf, APP_UPDATE_ADDRESS+uiOffsetAddr, ucLen, FALSE);
		uiOffsetAddr += ucLen;
	}
#endif



	//数据完成后，保存更新标志和数据长度，重启系统
	if(usIndex>=usTotalBao-1)
	{
		UpdateFlagBuff[0] =0xC5;//更新完成标志
		//数据长度
		UpdateFlagBuff[4] = (u8)(uiOffsetAddr>>24);//高位
		UpdateFlagBuff[3] = (u8)(uiOffsetAddr>>16);//中高位
		UpdateFlagBuff[2] = (u8)(uiOffsetAddr>>8);//中低位
		UpdateFlagBuff[1] = (u8)(uiOffsetAddr);//低位

		//Drv_FlashWrite_8bit(APP_UPDATE_CFG_ADDR, sizeof(UpdateFlagBuff), UpdateFlagBuff);//保存更新标志
		Drv_FlashWrite(UpdateFlagBuff, APP_UPDATE_CFG_ADDR, sizeof(UpdateFlagBuff), FALSE);//保存更新标志

		uiOffsetAddr=0;

		//系统复位重启
		//Drv_SystemReset();
	}

	uiLastBaoInDex = usIndex;
}

void bufExchangeHL2pos(char *buf ,int spos)
	//BUF指定位置对调3字节, spos和spos+1的2字节对调
{
	unsigned char c;
	c = buf[spos];
	buf[spos] = buf[spos + 1];
	buf[spos + 1] = c;
}


void ushort2Buf(char *buf,unsigned short *sc)
	//unsigned short转换到BUF
{
	memcpy(buf, sc, 2);
	bufExchangeHL2pos(buf,0);
}

void UartSend_nav2up_SetHead(char fromdev, unsigned short baglen,unsigned short cmd)
	//包头设置
{
	unsigned short sc;
	char c;
	sc = COMDATABAG_HEADER;
	ushort2Buf(&UartTxBuf[0],&sc);
	UartTxBuf[2] = fromdev;
	UartTxBuf[3] = COMDEVICEID_UP;
	sc = baglen - 6 - 2;
	ushort2Buf(&UartTxBuf[4],&sc);
	sc = cmd;
	ushort2Buf(&UartTxBuf[6],&sc);
}

void UartSend_nav2up_SetEnd(unsigned char baglen)
	//包尾设置
{
	static unsigned short SendBagCount_nav	=	0;									//有效数据包计数
	unsigned short sc = SendBagCount_nav;
	ushort2Buf(&UartTxBuf[baglen - 4],&sc);
	if (SendBagCount_nav>=0xffff){SendBagCount_nav 	= 0;}
	else{	SendBagCount_nav++;}

	unsigned char crc = 0;
	crc =  crc_verify_8bit(&UartTxBuf[6],baglen - 6 - 2) ;
	UartTxBuf[baglen - 2] = crc;

	UartTxBuf[baglen - 1] =COMDATABAG_END;
}


void UartSend_combag_setheadend_senddo_uart6(char fromdev,unsigned short baglen , unsigned short head)
	//设置发送数据包的包头包尾,并发送-uart4
{
	//包头
	UartSend_nav2up_SetHead(fromdev,baglen ,head);
	//包尾
	UartSend_nav2up_SetEnd(baglen);

	//发送
	UpdateFirmsendmsg((char*)&UartTxBuf, baglen);
}


void UartSend_UpdateFirm(unsigned short cmd)
	//发送
{
	unsigned short baglen = sizeof(UpdateFirmWareTx);

	memset(UartTxBuf,0,baglen);

	//信息处理
	memcpy(&UartTxBuf[8], &UpdateFirmWareTx.info, baglen -10);

	//UartDmaSend_nav2up_allHLReset();

	//包头包尾设置并发送
	UartSend_combag_setheadend_senddo_uart6(COMDEVICEID_NAV,baglen,cmd);
}


//固件更新处理
void Uart_UpdateFirm_nav(u16 cmd_nav2up)
{
	u8 ucSystemResetFlag=0;//系统重启标志
	static u8 UpdateSuccessful=0;//判断是否数据接收完成

	if(UpdateFirmWareRx.ender.check == CheckSum(UpdateFirmWareRx))//校验通过
	{
		UpdateFirmWareTx.info.ucRxFlag=1;//数据正确
							//固件处理
		if(UpdateFirmWareRx.info.ucUpdateType==UPDATE_FIRM_START)//开始更新
		{
			UpdateFirmWareTx.info.ucUpdateType = UPDATE_FIRM_START;
			UpdateFirmWareTx.info.usReplyType=VerSionSoft;//回复软件版本信息
			g_StartUpdateFirm=1;
			UpdateSuccessful=0;
		}
		else if(UpdateFirmWareRx.info.ucUpdateType==UPDATE_FIRM_SEND)//发送数据包
		{
			UpdateFirmWareTx.info.ucUpdateType = UPDATE_FIRM_SEND;
			UpdateFirmWareTx.info.usReplyType=UpdateFirmWareRx.info.usBaoIndex;//回复包序号
			//接收到数据后执行文件处理
			UpdateFileHandle(UpdateFirmWareRx.info.ucUpdateData,UpdateFirmWareRx.info.usBaoIndex,UpdateFirmWareRx.info.usTotalBao,UpdateFirmWareRx.info.ucLength);
			UpdateSuccessful=1;//数据接收完成
		}
		else if(UpdateFirmWareRx.info.ucUpdateType==UPDATE_FIRM_END)//结束更新
		{
			UpdateFirmWareTx.info.ucUpdateType = UPDATE_FIRM_END;
			ucSystemResetFlag=1;
			g_StartUpdateFirm=0;
		}
		else if(UpdateFirmWareRx.info.ucUpdateType==UPDATE_FIRM_STOP)//停止更新
		{
			ucSystemResetFlag=1;
			UpdateFirmWareTx.info.ucUpdateType = UPDATE_FIRM_STOP;
			g_StartUpdateFirm=0;
		}
	}
	else//校验未通过
	{
		UpdateFirmWareTx.info.ucRxFlag=2;//数据异常，下位机重发
	}

	//一次数据处理完成。回复上位机
	UartSend_UpdateFirm(cmd_nav2up);
	if((ucSystemResetFlag==1)&&(UpdateSuccessful==1))
	{
		//系统复位重启
		//delay_ms(1000);
		Drv_SystemReset();
	}

}


void UartDataHandle(void)
	//串口接收到一个数据包后处理
{
	static uint32_t error_count = 0;
	const uint32_t MAX_ERROR_COUNT = 10;  // 允许的最大错误次数

	// 检查数据包头部是否正确
	if((grxbuffer6[0]!=0x55)||(grxbuffer6[1]!=0xAA))//数据错误时，防止重启，防止内部测试或者调试时，数据丢失
	{
		error_count++;

		// 清空接收缓冲区，准备接收新数据
		extern int grxlen6, grxst6;
		grxlen6 = 0;
		grxst6 = 0;
		memset(grxbuffer6, 0, 64);

		// 只有在连续错误次数过多时才重启系统
		if(error_count >= MAX_ERROR_COUNT)
		{
			// 记录错误信息（如果有日志系统的话）
			// printf("UART6: Too many invalid packets, system reset\r\n");
			Drv_SystemReset();
		}
		return;  // 直接返回，不处理无效数据
	}

	// 如果收到有效数据，重置错误计数
	error_count = 0;

	// 处理有效数据包
	memcpy(&UpdateFirmWareRx,grxbuffer6,64);//复制接收数据
	unsigned short cmd = UpdateFirmWareRx.head.cmd;

	// 清空接收缓冲区和相关变量
	extern int grxlen6, grxst6;
	grxlen6 = 0;
	grxst6 = 0;
	memset(grxbuffer6,0,64);

	switch(cmd)
	{
		case COMDATABAG_UPCMD_updatefirmget:	//固件更新命令
			Uart_UpdateFirm_nav(COMDATABAG_UPCMD_updatefirmback);
			break;
		default:
			// 处理未知命令，增加容错性
			break;
	}
}




//FLASH测试程序
u8 WriteTestData[20] = {0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0A,0x0B,0x0C,0x0D,0x0E,0x0F,0x06,0x04,0x55,0x15};
u8 WriteTestData1[20] = {0x0A,0x0B,0x0C,0x0D,0x0E,0x0F,0x43,0x48,0x69,0x7A,0xCB,0xAC,0x1D,0x6E,0x8F,0x06,0x04,0x35,0x97,0xAA};

u32 ReadTestData[10];
u32 AppReadTestData[10];

void FlashTest(void)
{
	u8 temp[5];
  u32 ReadAppData[100];
  u8 ReadAppData1[100];
	//模拟写入到更新区
	/*memset(ReadTestData,0,sizeof(ReadTestData));
	Drv_FlashWrite(WriteTestData,APP_UPDATE_ADDRESS,20,TRUE);
	Drv_FlashRead(ReadTestData,APP_UPDATE_ADDRESS,5);

	Drv_FlashWrite(WriteTestData1,APP_UPDATE_ADDRESS+20,20,FALSE);
	Drv_FlashRead(ReadTestData+5,APP_UPDATE_ADDRESS+20,5);


	//模拟Bootloader读取更新区数据，并写入运行区
	memset(ReadTestData,0,sizeof(ReadTestData));
	Drv_FlashRead(ReadTestData,APP_UPDATE_ADDRESS,10);
	Drv_FlashWrite1(ReadTestData,FLASH_FACTORY_ADDR,10,TRUE);

	//读取运行区数据，检查是否正确
	memset(AppReadTestData,0,sizeof(AppReadTestData));
	Drv_FlashRead(AppReadTestData,FLASH_FACTORY_ADDR,10);*/

  	memset(ReadAppData,0,sizeof(ReadAppData));
	Drv_FlashRead(ReadAppData,APP_UPDATE_ADDRESS,25);
  Drv_FlashRead_8bit(ReadAppData1,APP_UPDATE_ADDRESS, 100);

	Drv_FlashRead_8bit(temp,APP_UPDATE_CFG_ADDR, 5);
}


