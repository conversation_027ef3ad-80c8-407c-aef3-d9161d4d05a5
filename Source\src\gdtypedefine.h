/*
 * Copyright (c) 2022-2028, INS Development Team
 *
 *
 * Change Logs:
 * Date           Author       Notes
 * 2023-06-04     Bill     	   the first version
 *
 */

#ifndef __GDTYPEDEFINE_H__
#define __GDTYPEDEFINE_H__
#include "appdefine.h"


#pragma pack(1)
//#define c_txmsgdatasize			8
//typedef struct _txmsgserialport {
//	unsigned char head0;
//	unsigned char head1;
//	short len;				//sizeof(txmsgserialport_t)
//	unsigned char cmd;
//	int parameter0;			//c_msg_ask_retransmission   0:-list index   1-retrans index
//	int parameter1;
//	int msglistindex;
//	int msgindex;
//	unsigned char data[c_txmsgdatasize];
//	unsigned char checksum;
//	unsigned char tail;
//} txmsgserialport_t;



typedef struct gdwrxdatainfo {
    unsigned short  navi_test_info_counter;
    unsigned int	gnssInfo_gpsweek;
    unsigned int 	gnssInfo_gpssecond;
    unsigned char   navi_test_info_pps_en;
    unsigned char   navi_test_info_gps_valid;

    unsigned char   ppsDelay;
    
    float   navi_test_info_gyroX;
    float   navi_test_info_gyroY;
    float   navi_test_info_gyroZ;
    float   navi_test_info_sensor_temp;
    float   navi_test_info_accelX;
    float   navi_test_info_accelY;
    float   navi_test_info_accelZ;
    double  gnssInfo_Lon;
    double  gnssInfo_Lat;
    float   gnssInfo_Altitude;
    float   gnssInfo_ve;
    float   gnssInfo_vn;
    float   gnssInfo_vu;
    float   gnssInfo_Pitch;
    float   gnssInfo_Roll;
    float   gnssInfo_Heading;
    unsigned char   gnssInfo_PositioningState;
    unsigned int    gnssInfo_rtkStatus;
    unsigned char   gnssInfo_StarNum;
    unsigned int    canInfo_counter;
    float   canInfo_data_WheelSpeed_Front_Left;
    float   canInfo_data_WheelSpeed_Front_Right;
    float   canInfo_data_WheelSpeed_Back_Left;
    float   canInfo_data_WheelSpeed_Back_Right;
    float   canInfo_data_WheelSteer;
    float   canInfo_data_OdoPulse_1;
    float   canInfo_data_OdoPulse_2;
    float   canInfo_data_Gear;
    unsigned char Adj_Nav_Standard_flag;
    double  Adj_gnssAtt_from_vehicle2_0;
    double  Adj_gnssAtt_from_vehicle2_1;
    double  Adj_gnssAtt_from_vehicle2_2;
    double  Adj_acc_off_0;
    double  Adj_acc_off_1;
    double  Adj_acc_off_2;
    double  Adj_gyro_off_0;
    double  Adj_gyro_off_1;
    double  Adj_gyro_off_2;

    float   gnss_trackTrue;

    unsigned int    result_Nav_Status;
    unsigned char   result_Nav_Standard_flag;
    unsigned char   result_imuSelect;
    unsigned char   result_memsType;
    unsigned char   result_use_gps_flag;
    unsigned char   result_fusion_source;

    unsigned int	gnssInfo_headingStatus;
    unsigned int    gnssInfo_gpssecond982;
	float fog0;
	#if c_txnavoutdata
    double  longitude;
    double  latitude;
    float   altitude;
    float   ve;
    float   vn;
    float   vu;
    float   Pitch;
    float   Roll;
    float   Heading;	
	#endif
} gdwrxdata_t;

typedef struct gdwrxdatainfoTX {
    unsigned short head;
    unsigned short len;         //packet all length
    unsigned short type;        //data type  1: imu     2: cacv     3: pps      4:gnss      5:magnetometer  6:gd watch
    gdwrxdata_t  gdwdata;
    float tt;
    int packet;
    int packetT;
    unsigned short checksum;
} gdwrxdataTX_t;

#define	c_msg_ask_scha63xcacv		0x12
#define	c_msg_ask_retransmission	0x26
#define	c_msg_ask_driversettings	0x39

#define GD_DRIVERSDATA_MAXCOUNT     200
#define DRIVERSDATATYPE_GNSS        1
#define DRIVERSDATATYPE_IMU         2
#define DRIVERSDATATYPE_CAN         3
#define DRIVERSDATATYPE_PPS         4
#define DRIVERSDATATYPE_GDW         6

typedef struct _driversdatatosimulator {
    int driversdatatype;    //0: null   1:-gnss   2-imu   3-can     4-pps   5-      6-gd watch
    int driverspacket;
    union {
        //rxonecaninfo_t  can;
        //imuorgdatasendtopc_t    imu;
        //gnessrxdataTX_t gnss;
        //gnessppsTX_t    pps;
        gdwrxdataTX_t   gdw;
    } data;
} driversdatatosimulator_t;

typedef struct _driversdatatosimulatorlist {
    int st;
    int size;
    driversdatatosimulator_t    driversdata[GD_DRIVERSDATA_MAXCOUNT];
} driversdatatosimulatorlist_t;


#define c_txmsgdatasize			8
typedef struct _txmsgserialport {
	unsigned char head0;
	unsigned char head1;
	short len;				//sizeof(txmsgserialport_t)
	unsigned char cmd;
	int parameter0;			//c_msg_ask_retransmission   0:-list index   1-retrans index
	int parameter1;
	int msglistindex;
	int msgindex;
	unsigned char data[c_txmsgdatasize];	
	unsigned char checksum;
	unsigned char tail;
} txmsgserialport_t;


typedef struct _driversettings {
//	char projectname[15];
//	char mcutype;
//	int imuframe;
//	int gnssframe;
//	int canfrmae;
//	int ppsframe;
//	int gdwframe;
//	int magnetframe;
//	int pressureframe;
	char projectname[16];
	char mcutype;
	char datatype;
	int imuframe;
	int gnssframe;
	int canfrmae;
	int ppsframe;
	int gdwframe;
	int magnetframe;
	int pressureframe;
	int opticalgryoframe;
	int reserved[20];
} driversettings_t;

typedef struct _driversettingstopc {
	unsigned short head;
	unsigned short len;         //packet all length
	unsigned short type;    //data type  1: imu     2: cacv		6: GD watch		7: driversettings
	driversettings_t settings;
	unsigned short checksum;
} driversettingstopc_t;

#pragma pack()

extern  int gdriverspacket;
extern  int ggdworgdata_packet;
extern  gdwrxdataTX_t	gtmpgdrx;
extern  driversdatatosimulatorlist_t	gdriverdatalist;
extern  driversettingstopc_t	gdriversettings;
extern	float gfog0;



extern	int mcusendtopcdriversdata(int cmd, int listindex, int driverindex);
extern	int initializationdriversettings(void);


#endif  //__GDTYPEDEFINE_H__
