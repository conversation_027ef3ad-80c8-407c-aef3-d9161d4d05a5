/***********************************************************************************
nav imu module header
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|DengWei       |  2023-5-17          | First Creation             |
|-----------+---------------+-------------------|  
***********************************************************************************/
#ifndef __NAV_IMU_H__
#define __NAV_IMU_H__
#include "nav_type.h"

	
//º¯ÊýÉùÃ÷
extern void Get_Param_Data(_NAV_Data_Full_t* NAV_Data_Full_temp,CombineDataTypeDef* CombineData_p);  //ÊäÈëÊý¾Ý»ñÈ¡
extern void Get_IMU_Data(_NAV_Data_Full_t* NAV_Data_Full_temp,CombineDataTypeDef* CombineData_p); //´«¸ÐÆ÷Êý¾Ý»ñÈ¡
extern void Load_Calib_Parms(_NAV_Data_Full_t* NAV_Data_Full_temp,CombineDataTypeDef* CombineData_p);   
extern unsigned char nav_calib(double *original,_calib_t* pcalib,double *standard);
extern void Load_Standard_Data(_NAV_Data_Full_t* NAV_Data_Full_p,CombineDataTypeDef* CombineData_p);
extern void savebuff();
#endif

