/*!
    \file  main.h
    \brief the header file of main 
*/

/*
    Copyright (C) 2016 GigaDevice

    2016-08-15, V1.0.0, firmware for GD32F4xx
*/

#ifndef __FPGAD_H
#define __FPGAD_H
#include "gd32f4xx_can.h"
#include "frame_analysis.h"
#include "appdefine.h"



typedef struct _fpgadata {
	unsigned short	DATA_LEN;	//00
	unsigned short	NUM_CLK;	//01
	short	Dtemp_data;	//02	
	short	Utemp_data;	//03
	short	accZ_data;	//04
	short	accY_data;	//05
	short	accX_data;	//06
	short	rateZ_data;	//07
	short	rateY_data;	//08
	short	rateX_data;	//09
	unsigned short	hGPSData_gpsweek;		//0a
	unsigned short	hGPSData_gpssecond0;	//0b
	unsigned short	hGPSData_gpssecond1;	//0c
	unsigned short	hGPSData_headingStatus;	//0d
	unsigned short	hGPSData_rtkStatus;		//0e
	unsigned short	hGPSData_vn0;	//0f
	unsigned short	hGPSData_vn1;	//10
	unsigned short	hGPSData_ve0;	//11
	unsigned short	hGPSData_ve1;	//12
	unsigned short	hGPSData_vu0;	//13
	unsigned short	hGPSData_vu1;	//14
	unsigned short	hGPSData_Heading0;	//15
	unsigned short	hGPSData_Heading1;	//16
	unsigned short	hGPSData_Pitch0;	//17
	unsigned short	hGPSData_Pitch1;	//18
	unsigned short	hGPSData_Roll0;		//19
	unsigned short	hGPSData_Roll1;		//1a
	unsigned short	hGPSData_Lat0;	//1b
	unsigned short	hGPSData_Lat1;	//1c
	unsigned short	hGPSData_Lat2;	//1d
	unsigned short	hGPSData_Lat3;	//1e
	unsigned short	hGPSData_Lon0;	//1f
	unsigned short	hGPSData_Lon1;	//20
	unsigned short	hGPSData_Lon2;	//21
	unsigned short	hGPSData_Lon3;	//22
	unsigned short	hGPSData_Alt0;	//23
	unsigned short	hGPSData_Alt1;	//24
	unsigned short	hGPSData_Alt2;	//25
	unsigned short	hGPSData_Alt3;	//26
	unsigned short	GPGGA_STAR;		//27
	unsigned short	HEADING_BAS0;	//28
	unsigned short	HEADING_BAS1;	//29
	unsigned short	GPRMC_POS;		//2a
	unsigned short	GPRMC_LON;		//2b
	unsigned short	GPRMC_LAT;		//2c
	unsigned short	GPRMC_TRA[3];	//2d 2e 2f
//	unsigned short	Heading0;
//	unsigned short	Heading1;
//	unsigned short	Pitch0;
//	unsigned short	Pitch1;
	unsigned short	gpssecond9820;	//30
	unsigned short	gpssecond9821;	//31
	unsigned short	gnsstimedelay0;	//32
	unsigned short	gnsstimedelay1;	//33
	unsigned short	reserveD;		//34
	unsigned short	velStatus;		//35
	
	unsigned short	magGrp0; //36
	unsigned short	magGrp1; //37
	unsigned short	magGrp2; //38
	
	unsigned short	fog_x0;	//39
	unsigned short	fog_x1;	//3a
	unsigned short	fog_y0;	//3b
	unsigned short	fog_y1;	//3c
	unsigned short	fog_z0;	//3d
	unsigned short	fog_z1;	//3e
	unsigned short	fog_tempx;	//3f
	unsigned short	fog_tempy;	//40
	unsigned short	fog_tempz;	//41
	
	unsigned short	axis3_accx0;	//42
	unsigned short	axis3_accx1;	//43
	unsigned short	axis3_accy0;	//44
	unsigned short	axis3_accy1;	//45
	unsigned short	axis3_accz0;	//46
	unsigned short	axis3_accz1;	//47
	unsigned short	axis3_temp;		//48
	
	unsigned short	VERSION;   //	49
	unsigned short	checksum;
} fpgadata_t;

extern	fpgadata_t	gpagedata;






#define	U4RX_MAXCOUNT		(1024 * 40)	//(1024 * 4)
#define	FRAMEPARSEBUFSIZE	(512 * 2)
extern	unsigned char grxbuffer[U4RX_MAXCOUNT];
extern	unsigned char grxbuffer6[U4RX_MAXCOUNT];
extern	int grxlen, grxst;
extern	unsigned char gframeParsebuf[FRAMEPARSEBUFSIZE];
extern	can_receive_message_struct gCanRxBuf;


extern	void printf_uart4(int type, char *fmt, ...);
extern	void uart4sendmsg(char *txbuf, int size);
extern	void uart6sendmsg(char *txbuf, int size);
extern	void uart4sendmsg_billdebug(char *txbuf, int size);
extern	void uart4sendmsg_canout(can_receive_message_struct *receive_message);
extern	void analysisRxdata(void);

#endif /* __FPGAD_H */


