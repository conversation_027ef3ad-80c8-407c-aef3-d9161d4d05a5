#ifndef ____BSP_GPIO_H____
#define ____BSP_GPIO_H____

#include "gd32f4xx.h"

//////////////////////////////////////////////////////////////////////////////////////////////////////
//引脚定义
//////////////////////////////////////////////////////////////////////////////////////////////////////
//输出引脚
#define ARM2_OUTPUT_ARM1_IO_PORT			GPIOE
#define ARM2_OUTPUT_ARM1_IO_PIN				GPIO_PIN_4

#define UM982_POWER_IO_PORT					GPIOA
#define UM982_POWER_IO_PIN					GPIO_PIN_5

#define Z_AXIS_5V_POWER_IO_PORT				GPIOH
#define Z_AXIS_5V_POWER_IO_PIN				GPIO_PIN_14

#define MEMS_3V3_POWER_IO_PORT				GPIOH
#define MEMS_3V3_POWER_IO_PIN				GPIO_PIN_15

#define ARM1_POWER_IO_PORT					GPIOB
#define ARM1_POWER_IO_PIN					GPIO_PIN_1

#define LED_SRAM_IO_PORT					GPIOH
#define LED_SRAM_IO_PIN						GPIO_PIN_7

#define LED_WHEEL_IO_PORT					GPIOE
#define LED_WHEEL_IO_PIN					GPIO_PIN_5

#define LED_STATE_IO_PORT					GPIOE
#define LED_STATE_IO_PIN					GPIO_PIN_2

#define CAN1_IO_PORT						GPIOA
#define CAN1_IO_PIN							GPIO_PIN_10

#define CAN2_IO_PORT						GPIOB
#define CAN2_IO_PIN							GPIO_PIN_15

#define ETH_RST_IO_PORT						GPIOE
#define ETH_RST_IO_PIN						GPIO_PIN_6

#define CAN1_STB_IO_PORT					GPIOA
#define CAN1_STB_IO_PIN						GPIO_PIN_8

#define CAN2_STB_IO_PORT					GPIOB
#define CAN2_STB_IO_PIN						GPIO_PIN_11



//输入引脚
#define ARM2_IO_PORT						GPIOE
#define ARM2_IO_PIN							GPIO_PIN_15



//PWM引脚
#define PWM_IO_PORT							GPIOA
#define PWM_IO_PIN							GPIO_PIN_0

//////////////////////////////////////////////////////////////////////////////////////////////////////
//引脚功能定义
//////////////////////////////////////////////////////////////////////////////////////////////////////
//IO输出控制
#define UM982_PowerON()				gpio_bit_set(UM982_POWER_IO_PORT,UM982_POWER_IO_PIN)
#define UM982_PowerOFF()			gpio_bit_reset(UM982_POWER_IO_PORT,UM982_POWER_IO_PIN)

#define Z_AXIS_5V_PowerON()			gpio_bit_set(Z_AXIS_5V_POWER_IO_PORT,Z_AXIS_5V_POWER_IO_PIN)
#define Z_AXIS_5V_PowerOFF()		gpio_bit_reset(Z_AXIS_5V_POWER_IO_PORT,Z_AXIS_5V_POWER_IO_PIN)

#define MEMS_3V3_PowerON()			gpio_bit_set(MEMS_3V3_POWER_IO_PORT,MEMS_3V3_POWER_IO_PIN)
#define MEMS_3V3_PowerOFF()			gpio_bit_reset(MEMS_3V3_POWER_IO_PORT,MEMS_3V3_POWER_IO_PIN)

#define ARM1_PowerON()				gpio_bit_set(ARM1_POWER_IO_PORT,ARM1_POWER_IO_PIN)
#define ARM1_PowerOFF()				gpio_bit_reset(ARM1_POWER_IO_PORT,ARM1_POWER_IO_PIN)

#define LED_SRAM_ON()				gpio_bit_set(LED_SRAM_IO_PORT,LED_SRAM_IO_PIN)
#define LED_SRAN_OFF()				gpio_bit_reset(LED_SRAM_IO_PORT,LED_SRAM_IO_PIN)

#define LED_SRAM_Toggle()			gpio_bit_toggle(LED_SRAM_IO_PORT,LED_SRAM_IO_PIN)
//*********
#define 	LED_WHEEL_OFF()			gpio_bit_set(LED_WHEEL_IO_PORT,LED_WHEEL_IO_PIN)
#define  LED_WHEEL_ON()				gpio_bit_reset(LED_WHEEL_IO_PORT,LED_WHEEL_IO_PIN)

#define LED_Wheel_Toggle()			gpio_bit_toggle(LED_WHEEL_IO_PORT,LED_WHEEL_IO_PIN)

#define LED_STATE_OFF()				gpio_bit_set(LED_STATE_IO_PORT,LED_STATE_IO_PIN)
#define LED_STATE_ON()				gpio_bit_reset(LED_STATE_IO_PORT,LED_STATE_IO_PIN)
//***************
#define LED_State_Toggle()			gpio_bit_toggle(LED_STATE_IO_PORT,LED_STATE_IO_PIN)

#define CAN1_Enable()				gpio_bit_set(CAN1_IO_PORT,CAN1_IO_PIN)
#define CAN1_Disable()				gpio_bit_reset(CAN1_IO_PORT,CAN1_IO_PIN)

#define CAN2_Enable()				gpio_bit_set(CAN2_IO_PORT,CAN2_IO_PIN)
#define CAN2_Disable()				gpio_bit_reset(CAN2_IO_PORT,CAN2_IO_PIN)

#define ETH_RST_High()				gpio_bit_set(ETH_RST_IO_PORT,ETH_RST_IO_PIN)
#define ETH_RST_Low()				gpio_bit_reset(ETH_RST_IO_PORT,ETH_RST_IO_PIN)

#define ARM2_OUTPUT_ARM1_High()		gpio_bit_set(ARM2_OUTPUT_ARM1_IO_PORT,ARM2_OUTPUT_ARM1_IO_PIN)
#define ARM2_OUTPUT_ARM1_Low()		gpio_bit_reset(ARM2_OUTPUT_ARM1_IO_PORT,ARM2_OUTPUT_ARM1_IO_PIN)

#define CAN1_STB_High()				gpio_bit_set(CAN1_STB_IO_PORT,CAN1_STB_IO_PIN)
#define CAN1_STB_Low()				gpio_bit_reset(CAN1_STB_IO_PORT,CAN1_STB_IO_PIN)

#define CAN2_STB_High()				gpio_bit_set(CAN2_STB_IO_PORT,CAN2_STB_IO_PIN)
#define CAN2_STB_Low()				gpio_bit_reset(CAN2_STB_IO_PORT,CAN2_STB_IO_PIN)



//IO输入定义
#define ARM2_IN_IO_Read()			gpio_input_bit_get(ARM2_IO_PORT, ARM2_IO_PIN)






void bsp_gpio_init(void);



#endif //____BSP_GPIO_H____
