#ifndef ____BSP_CAN_H____
#define ____BSP_CAN_H____

#include "gd32f4xx.h"
//#include "bsp_fmc.h"
#include "frame_analysis.h"



#define VEHICLE_TYPE_WuLing_MiniEV			1		//五菱 MiniEv
#define VEHICLE_TYPE_YiQiJieFang_J6V		2		//一汽解放 J6V
#define VEHICLE_TYPE_AutoBots_RoboMix		3		//AutoBots-RoboMix
#define VEHICLE_TYPE_BoLeiDun				4		//博雷顿
#define VEHICLE_TYPE_DongFeng_YueXiang		5		//东风悦翔
#define VEHICLE_TYPE_YiQiJieFang_J7			6		//一汽解放 J7
#define VEHICLE_TYPE_DongFengFengShen_E70	7		//东风风神E70
#define	VEHICLE_TYPE_BYD					8

//#define VEHICLE_TYPE_USED					VEHICLE_TYPE_AutoBots_RoboMix
#define VEHICLE_TYPE_USED					VEHICLE_TYPE_BYD


#if VEHICLE_TYPE_USED == VEHICLE_TYPE_WuLing_MiniEV
//CAN ID 从 车辆 接收
#define		CAN_ID_GEAR				0x155		// 档位挡位
#define		CAN_ID_FRONT_WHEEL		0x348		// 车轮前轮
#define		CAN_ID_BACK_WHEEL		0x34A		// 车轮后轮
#define		CAN_ID_WHEEL_STEER		0x350		// 车轮转向轮
#define		CAN_ID_ODOMETER			0x35A		// 车轮里程计
#define		CAN_ID_OTA				0x7FF		// OTA

//CAN ID 向 车辆 发送
#define CAN_ID_INS_ACC				0x500		//惯导系统发送	加速度
#define CAN_ID_INS_GYRO				0x501		//惯导系统发送	陀螺仪
#define CAN_ID_INS_ANGLE			0x502		//惯导系统发送	俯仰角、横滚角、方位角
#define CAN_ID_INS_HEIGHT			0x503		//惯导系统发送	高度和时间
#define CAN_ID_INS_POSITION			0x504		//惯导系统发送	经纬度
#define CAN_ID_INS_SPEED			0x505		//惯导系统发送	速度
#define CAN_ID_INS_INFO				0x506		//惯导系统发送	导航信息
#define CAN_ID_INS_STD				0x507		//惯导系统发送	标准差
#define CAN_ID_INS_GPSTIME			0x508		//惯导系统发送	GPS时间

#define CAN_ID_INS_CALI				0x020		//IMU标定状态
#define CAN_ID_INS_SYS_STATUS		0x030		//INS系统状态
#define CAN_ID_INS_GEAR				0x110		//档位
#define CAN_ID_INS_VEL_SPEED		0x010		//车辆速度

#endif // VEHICLE_TYPE_USED == VEHICLE_TYPE_WuLing_MiniEV


#if VEHICLE_TYPE_USED == VEHICLE_TYPE_AutoBots_RoboMix
//#if VEHICLE_TYPE_USED == VEHICLE_TYPE_BYD

//CAN ID 从 车辆 接收
#define		CAN_ID_GEAR				0x1F5		// 档位挡位
#define		CAN_ID_FRONT_WHEEL		0x348		// 车轮前轮
#define		CAN_ID_BACK_WHEEL		0x34A		// 车轮后轮
#define		CAN_ID_WHEEL_STEER		0x350		// 车轮转向轮
#define		CAN_ID_ODOMETER			0x35A		// 车轮里程计
#define		CAN_ID_OTA				0x7FF		// OTA

//CAN ID 向 车辆 发送
#define CAN_ID_INS_ACC				0x500		// - 惯导系统发送	加速度
#define CAN_ID_INS_GYRO				0x501		// - 惯导系统发送	陀螺仪
#define CAN_ID_INS_ANGLE			0x502		// - 惯导系统发送	俯仰角、横滚角、Roll角
#define CAN_ID_INS_HEIGHT			0x503		// - 惯导系统发送	高度和时间
#define CAN_ID_INS_POSITION			0x504		// - 惯导系统发送	经纬度
#define CAN_ID_INS_SPEED			0x505		// - 惯导系统发送	速度
#define CAN_ID_INS_INFO				0x506		// - 惯导系统发送	导航信息
#define CAN_ID_INS_STD0				0x507		// - 惯导系统发送	标准差
#define CAN_ID_INS_STD1				0x508		// - 惯导系统发送	标准差
#define CAN_ID_INS_POSaTEMP			0x509		// - 惯导系统发送	pose and temperature

#define CAN_ID_INS_CALI				0x020		//IMU标定状态
#define CAN_ID_INS_SYS_STATUS		0x030		//INS系统状态
#define CAN_ID_INS_GEAR				0x110		//档位
#define CAN_ID_INS_VEL_SPEED		0x010		//车辆速度

#endif // VEHICLE_TYPE_USED == VEHICLE_TYPE_BYD

#if VEHICLE_TYPE_USED == VEHICLE_TYPE_YiQiJieFang_J6V
//CAN ID 从 车辆 接收
#define		CAN_ID_GEAR				0x155		// 档位挡位
#define		CAN_ID_WHEEL			0x8FE6E0B	// 车轮信息
#define		CAN_ID_WHEEL_STEER		0x350		// 车轮转向轮
#define		CAN_ID_ODOMETER			0x35A		// 车轮里程计
#define		CAN_ID_OTA				0x1FFFFFFF	// OTA

//CAN ID 向 车辆 发送
#define CAN_ID_INS_ACC				0x500		//惯导系统发送	加速度
#define CAN_ID_INS_GYRO				0x501		//惯导系统发送	陀螺仪
#define CAN_ID_INS_ANGLE			0x502		//惯导系统发送	俯仰角、横滚角、方位角
#define CAN_ID_INS_HEIGHT			0x503		//惯导系统发送	高度和时间
#define CAN_ID_INS_POSITION			0x504		//惯导系统发送	经纬度
#define CAN_ID_INS_SPEED			0x505		//惯导系统发送	速度
#define CAN_ID_INS_INFO				0x506		//惯导系统发送	导航信息
#define CAN_ID_INS_STD				0x507		//惯导系统发送	标准差
#define CAN_ID_INS_GPSTIME			0x508		//惯导系统发送	GPS时间

#endif //VEHICLE_TYPE_USED == VEHICLE_TYPE_YiQiJieFang_J6V



//#if VEHICLE_TYPE_USED == VEHICLE_TYPE_AutoBots_RoboMix
#if VEHICLE_TYPE_USED == VEHICLE_TYPE_BYD

//wu lin hong guang...
#define		CAN_ID_WL_GEAR			0x155		// 五菱档位
#define		CAN_ID_WL_FRONT_WHEEL	0x348		// 五菱前轮
#define		CAN_ID_WL_BACK_WHEEL	0x34A		// 五菱后轮

#define		CAN_ID_BYD_GEAR			0x1F5		// 比亚迪档位
#define		CAN_ID_BYD_FRONT_WHEEL	0x348		// 比亚迪前轮
#define		CAN_ID_BYD_BACK_WHEEL	0x34A		// 比亚迪后轮


//CAN ID 从 车辆 接收
//#define		CAN_ID_GEAR				0x1F5		// 档位挡位
//#define		CAN_ID_FRONT_WHEEL		0x348		// 车轮前轮
//#define		CAN_ID_BACK_WHEEL		0x34A		// 车轮后轮
#define		CAN_ID_WHEEL_STEER		0x350		// 车轮转向轮
#define		CAN_ID_ODOMETER			0x35A		// 车轮里程计
#define		CAN_ID_OTA				0x7FF		// OTA

//define		CAN_ID_STATE			0x304		// 车辆当前状态
//#define		CAN_ID_OTA				0x1FFFFFFF	// OTA

//CAN ID 向 车辆 发送
#define CAN_ID_INS_ACC				0x500		//惯导系统发送	加速度
#define CAN_ID_INS_GYRO				0x501		//惯导系统发送	陀螺仪
#define CAN_ID_INS_ANGLE			0x502		//惯导系统发送	俯仰角、横滚角、方位角
#define CAN_ID_INS_HEIGHT			0x503		//惯导系统发送	高度和时间
#define CAN_ID_INS_POSITION			0x504		//惯导系统发送	经纬度
#define CAN_ID_INS_SPEED			0x505		//惯导系统发送	速度
#define CAN_ID_INS_STATUS			0x506		//惯导系统发送	status
#define CAN_ID_INS_STD_HEADING		0x507		//惯导系统发送	Heading 标准差
#define CAN_ID_INS_TEMPERATURE		0x508		//惯导系统发送	Temperature Frame
//#define CAN_ID_INS_GPSTIME			0x108		//惯导系统发送	GPS时间
//#define CAN_ID_INS_CALI				0x020		//IMU标定状态
//#define CAN_ID_INS_SYS_STATUS		0x030		//INS系统状态
//#define CAN_ID_INS_GEAR				0x110  		//档位
//#define CAN_ID_INS_VEL_SPEED		0x010 		//车辆速度

#endif 

#if VEHICLE_TYPE_USED == VEHICLE_TYPE_BoLeiDun

//CAN ID 从 车辆 接收
#define		CAN_ID_STATE			0x18D0FF00		// 车辆当前状态
#define		CAN_ID_STEER			0x18D1FF00		// 车轮转向轮
#define		CAN_ID_OTA				0x1FFFFFFF		// OTA

//CAN ID 向 车辆 发送
#define CAN_ID_INS_ACC				0x500		//惯导系统发送	加速度
#define CAN_ID_INS_GYRO				0x501		//惯导系统发送	陀螺仪
#define CAN_ID_INS_ANGLE			0x502		//惯导系统发送	俯仰角、横滚角、方位角
#define CAN_ID_INS_HEIGHT			0x503		//惯导系统发送	高度和时间
#define CAN_ID_INS_POSITION			0x504		//惯导系统发送	经纬度
#define CAN_ID_INS_SPEED			0x505		//惯导系统发送	速度
#define CAN_ID_INS_INFO				0x506		//惯导系统发送	导航信息
#define CAN_ID_INS_STD				0x507		//惯导系统发送	标准差
#define CAN_ID_INS_GPSTIME			0x508		//惯导系统发送	GPS时间

#endif //VEHICLE_TYPE_USED == VEHICLE_TYPE_BoLeiDun

#if VEHICLE_TYPE_USED == VEHICLE_TYPE_DongFeng_YueXiang
//CAN ID 从 车辆 接收
#define		CAN_ID_GEAR				0x5C0		// 档位挡位
#define		CAN_ID_WHEEL			0xA3		// 车轮信息
#define		CAN_ID_ABS				0x4C0		// ABS信息
#define		CAN_ID_OTA				0x7FFFFF	// OTA

//CAN ID 向 车辆 发送
#define CAN_ID_INS_ACC				0x5C3		//惯导系统发送	加速度
#define CAN_ID_INS_GYRO				0x5C4		//惯导系统发送	陀螺仪
#define CAN_ID_INS_ANGLE			0x5C5		//惯导系统发送	俯仰角、横滚角、方位角
#define CAN_ID_INS_POSITION			0x5C6		//惯导系统发送	经纬度
#define CAN_ID_INS_SPEED			0x5C7		//惯导系统发送	速度
#define CAN_ID_INS_STATE			0x5CA		//惯导系统发送	状态信息
#define CAN_ID_INS_STD				0x5CB		//惯导系统发送	标准差
#define CAN_ID_INS_GPSTIME			0x5CC		//惯导系统发送	GPS时间
#define CAN_ID_INS_GPSWEEK			0x5CD		//惯导系统发送	GPS周
#endif // VEHICLE_TYPE_USED == VEHICLE_TYPE_DongFeng_YueXiang


#if VEHICLE_TYPE_USED == VEHICLE_TYPE_YiQiJieFang_J7

//CAN ID 从 车辆 接收
#define		CAN_ID_GEAR				0x1F5		// 档位挡位
#define		CAN_ID_WHEEL_FRONT		0x348		// 车轮前轮
#define		CAN_ID_WHEEL_BACK		0x34A		// 车轮后轮
#define		CAN_ID_OTA				0x1FFFFFFF	// OTA

//CAN ID 向 车辆 发送
#define CAN_ID_INS_ACC				0x500		//惯导系统发送	加速度
#define CAN_ID_INS_GYRO				0x501		//惯导系统发送	陀螺仪
#define CAN_ID_INS_ANGLE			0x502		//惯导系统发送	俯仰角、横滚角、方位角
#define CAN_ID_INS_HEIGHT			0x503		//惯导系统发送	高度和时间
#define CAN_ID_INS_POSITION			0x504		//惯导系统发送	经纬度
#define CAN_ID_INS_SPEED			0x505		//惯导系统发送	速度
#define CAN_ID_INS_INFO				0x506		//惯导系统发送	导航信息
#define CAN_ID_INS_STD				0x507		//惯导系统发送	标准差
#define CAN_ID_INS_GPSTIME			0x508		//惯导系统发送	GPS时间

#endif //VEHICLE_TYPE_USED == VEHICLE_TYPE_YiQiJieFang_J7

#if VEHICLE_TYPE_USED == VEHICLE_TYPE_DongFengFengShen_E70

//CAN ID 从 车辆 接收
#define		CAN_ID_GEAR				0x355		// 档位挡位
#define		CAN_ID_WHEEL			0xA3		// 车轮信息
#define		CAN_ID_OTA				0x1FFFFFFF	// OTA

//CAN ID 向 车辆 发送
#define CAN_ID_INS_ACC				0x500		//惯导系统发送	加速度
#define CAN_ID_INS_GYRO				0x501		//惯导系统发送	陀螺仪
#define CAN_ID_INS_ANGLE			0x502		//惯导系统发送	俯仰角、横滚角、方位角
#define CAN_ID_INS_HEIGHT			0x503		//惯导系统发送	高度和时间
#define CAN_ID_INS_POSITION			0x504		//惯导系统发送	经纬度
#define CAN_ID_INS_SPEED			0x505		//惯导系统发送	速度
#define CAN_ID_INS_INFO				0x506		//惯导系统发送	导航信息
#define CAN_ID_INS_STD				0x507		//惯导系统发送	标准差
#define CAN_ID_INS_GPSTIME			0x508		//惯导系统发送	GPS时间

#endif //VEHICLE_TYPE_USED == VEHICLE_TYPE_DongFengFengShen_E70

typedef struct bsp_can_t
{
	uint32_t canDev;
	rcu_periph_enum canClk;
	
	uint32_t canHGPIO;
	rcu_periph_enum canHGPIO_CLK;
	uint16_t canH_Pin;
	
	uint32_t canLGPIO;
	rcu_periph_enum canLGPIO_CLK;
	uint16_t canL_Pin;
	
	IRQn_Type canIRQ;
	uint32_t prioMain;
	uint32_t prioSub;
	
	can_parameter_struct CanPara;
	
	can_filter_parameter_struct CanFilter;
	
	can_receive_message_struct CanRxBuf;
	can_trasnmit_message_struct CanTxBuf;
	
	FlagStatus RecvdFlag;
}CANDevTypeDef;

typedef enum poll_data_type
{
    locating_info_prec = 0,
    speed_info_prec = 1,
    pos_info_prec = 2,
    dev_inter_temp = 22,
    gps_status     = 32,
    rotate_status = 33,
    gnss_baseline = 34,
    gnss_rtkStatus = 35,
    para_adj = 36,
    calib_rate = 37
} POLL_DATA_TypeDef;

extern CANDevTypeDef hCAN0,hCAN1;
extern uint8_t g_gear_detected;
extern uint8_t g_front_detected;
extern uint8_t g_back_detected;
extern uint8_t g_can0_rx_flag;
extern uint8_t g_can1_rx_flag;

extern uint8_t g_CAN_Count;

void bsp_can_init(CANDevTypeDef* hCan);
void bsp_can_transmit(CANDevTypeDef* hCan, can_trasnmit_message_struct* TxMsg);

void can_fill_std(CANDevTypeDef* hCan);
void can_fill_status(CANDevTypeDef* hCan);

void can_transmit_acc(CANDevTypeDef* hCan);
void can_transmit_gyro(CANDevTypeDef* hCan);
void can_transmit_angle(CANDevTypeDef* hCan);
void can_transmit_h(CANDevTypeDef* hCan);
void can_transmit_pos(CANDevTypeDef* hCan);
void can_transmit_speed(CANDevTypeDef* hCan);
void can_transmit_status(CANDevTypeDef* hCan);
void can_transmit_std_heading(CANDevTypeDef* hCan);
//void can_transmit_std_speed(CANDevTypeDef* hCan);
//void can_transmit_std_pose(CANDevTypeDef* hCan);
void can_transmit_temperature(CANDevTypeDef* hCan);

void can_transmit_data_info(CANDevTypeDef* hCan);
void can_transmit_std(CANDevTypeDef* hCan);
void can_transmit_gpstime(CANDevTypeDef* hCan);
void can_transmit_posatemp(CANDevTypeDef* hCan);
void can_transmit_cali(CANDevTypeDef* hCan);
void can_transmit_sys_status(CANDevTypeDef* hCan);
void can_transmit_gear(CANDevTypeDef* hCan);
void can_transmit_vel_speed(CANDevTypeDef* hCan);
void can_transmit_gpsweek(CANDevTypeDef* hCan);

#endif //____BSP_CAN_H____
