/***********************************************************************************
nav magnet module header
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|DengWei       |  2023-5-9          | First Creation             |
|-----------+---------------+-------------------|  
***********************************************************************************/
#ifndef __NAV_MAGNET_H__
#define __NAV_MAGNET_H__
#include "nav_type.h"

void Get_Magnet_Data(_NAV_Data_Full_t* NAV_Data_Full_p,CombineDataTypeDef* CombineData_p);
double MagInitHeading(double *Mag, double *att,double *pos);

#endif

