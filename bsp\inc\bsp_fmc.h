#ifndef ____BSP_FMC_H____
#define ____BSP_FMC_H____

#include "gd32f4xx.h"
#include "bsp_sys.h"

#define SDRAM_DEVICE0_ADDR							((uint32_t)0xC0000000)
#define SDRAM_DEVICE1_ADDR							((uint32_t)0xD0000000)

#define FPGA_BASE_ADDR ((uint32_t)0x60000000) 
#define FPGA_DRAM_ADDR ((uint32_t)0x64000000) 

#define DRAM_BASE_ADDR		FPGA_DRAM_ADDR
#define DRAM_DATA(offset)	*(volatile u16 *)(DRAM_BASE_ADDR + ((offset) << 1))

#define DRAM_SIZE			32768 // 32K x 16

#define ADDR_SHIFT(x) ((x)<<1)

typedef unsigned short USHORT;
typedef unsigned char UCHAR;

typedef enum
{
	UART_BAUDRATE_2400BPS = 0,
	UART_BAUDRATE_4800BPS,
	UART_BAUDRATE_9600BPS,
	UART_BAUDRATE_19200BPS,
	UART_BAUDRATE_38400BPS,
	UART_BAUDRATE_57600BPS,
	UART_BAUDRATE_115200BPS,
	UART_BAUDRATE_230400BPS,
	UART_BAUDRATE_384000BPS,
	UART_BAUDRATE_460800BPS,
	UART_BAUDRATE_7200BPS,
	UART_BAUDRATE_921600BPS,
	UART_BAUDRATE_2500000BPS
}EUartBaudrate;

typedef enum
{
	UART_PARITY_NONE = 0,
	UART_PARITY_ODD = 0x0C,
	UART_PARITY_EVEN = 0x08
}EUartParitybits;

typedef enum
{
	UART_STOPBIT_ONE = 0,
	UART_STOPBIT_TWO = 2
}EUartStopbits;

typedef enum
{
	UART_RS232 = 0,
	UART_RS422,
    UART_RS485,
	UART_DEFAULT
}EUartMode;

typedef enum
{
	UART_DISABLE = 0,
	UART_ENABLE = 1
}EUartEnable;

typedef enum
{
	UART_RXPORT_RS232_1 = 0,
	UART_RXPORT_COMPLEX_8 = 1,
	UART_RXPORT_COMPLEX_9 = 2,
	UART_RXPORT_COMPLEX_10 = 3,
	UART_RXPORT_COMPLEX_11 = 4,
	UART_RXPORT_COMPLEX_12 = 5,
	UART_RXPORT_COMPLEX_13 = 6,
	UART_RXPORT_COMPLEX_14 = 7,
	UART_RXPORT_COMPLEX_15 = 8,
	UART_RXPORT_NULL = 255
}EUartRxPort;

typedef enum
{
	UART_TXPORT_RS232_1 = 0,
	UART_TXPORT_COMPLEX_8 = 1,
	UART_TXPORT_COMPLEX_9 = 2,
	UART_TXPORT_COMPLEX_10 = 3,
	UART_TXPORT_COMPLEX_11 = 4,
	UART_TXPORT_COMPLEX_12 = 5,
	UART_TXPORT_COMPLEX_13 = 6,
	UART_TXPORT_COMPLEX_14 = 7,
	UART_TXPORT_COMPLEX_15 = 8,
	UART_TXPORT_NULL = 255
}EUartTxPort;


// Tx-Uart
#define UART_TBASE                  (FPGA_BASE_ADDR + 0x00)
#define UART_THR(com)               (volatile unsigned short*)(UART_TBASE + 0x00 + com * 0x0010) // Tx-Uart data send macro
#define UART_TBAUD(com)             (volatile unsigned short*)(UART_TBASE + 0x02 + com * 0x0010) // Tx-Uart baudrate set macro
#define UART_TCONFIG(com)           (volatile unsigned short*)(UART_TBASE + 0x04 + com * 0x0010) // Tx-Uart config(databits, stopbits, parity) set macro
// Rx-Uart
#define UART_RBASE                  (FPGA_BASE_ADDR + 0x100)
#define UART_RHR(com)               (volatile unsigned short*)(UART_RBASE + 0x00 + com * 0x0010) // Rx-Uart data send macro
#define UART_RBAUD(com)             (volatile unsigned short*)(UART_RBASE + 0x02 + com * 0x0010) // Tx-Uart baudrate set macro
#define UART_RCONFIG(com)           (volatile unsigned short*)(UART_RBASE + 0x04 + com * 0x0010) // Rx-Uart config(databits, stopbits, parity) set macro
#define UART_RFIFO_STATE(com)       (volatile unsigned short*)(UART_RBASE + 0x0c + com * 0x0010) // Rx-Uart FOFI state macro
// Mode choose
#define UART_MODE_SEL               (volatile unsigned short*)(UART_RBASE + 0x232) // R232/422 Choose

typedef enum
{
	SPACE_COM,
	SPACE_RAM,
}EFpgaSpace;

/* function declarations */
/* sdram peripheral initialize */
void exmc_synchronous_dynamic_ram_init(uint32_t sdram_device);
/* fill the buffer with specified value */
void fill_buffer(uint8_t *pbuffer, uint16_t buffer_lengh, uint16_t offset);
/* write a byte buffer(data is 8 bits) to the EXMC SDRAM memory */
void sdram_writebuffer_8(uint32_t sdram_device, uint8_t* pbuffer, uint32_t writeaddr, uint32_t numbytetowrite);
/* read a block of 8-bit data from the EXMC SDRAM memory */
void sdram_readbuffer_8(uint32_t sdram_device, uint8_t* pbuffer, uint32_t readaddr, uint32_t numbytetoread);



/* FMC sector information */
typedef struct
{
    uint32_t sector_name;                                         /*!< the name of the sector */
    uint32_t sector_num;                                          /*!< the number of the sector */
    uint32_t sector_size;                                         /*!< the size of the sector */
    uint32_t sector_start_addr;                                   /*!< the start address of the sector */
    uint32_t sector_end_addr;                                     /*!< the end address of the sector */
} fmc_sector_info_struct;

/* sector size */
#define SIZE_16KB                  ((uint32_t)0x00004000U)        /*!< size of 16KB*/
#define SIZE_64KB                  ((uint32_t)0x00010000U)        /*!< size of 64KB*/
#define SIZE_128KB                 ((uint32_t)0x00020000U)        /*!< size of 128KB*/
#define SIZE_256KB                 ((uint32_t)0x00040000U)        /*!< size of 256KB*/

/* FMC BANK address */
#define FMC_START_ADDRESS          FLASH_BASE                               /*!< FMC start address */
#define FMC_BANK0_START_ADDRESS    FMC_START_ADDRESS                        /*!< FMC BANK0 start address */
#define FMC_BANK1_START_ADDRESS    ((uint32_t)0x08100000U)                  /*!< FMC BANK1 start address */
#define FMC_SIZE                   (*(uint16_t *)0x1FFF7A22U)               /*!< FMC SIZE */
#define FMC_END_ADDRESS            (FLASH_BASE + (FMC_SIZE * 1024) - 1)     /*!< FMC end address */
#define FMC_MAX_END_ADDRESS        ((uint32_t)0x08300000U)                  /*!< FMC maximum end address */

/* FMC error message */
#define FMC_WRONG_SECTOR_NAME      ((uint32_t)0xFFFFFFFFU)        /*!< wrong sector name*/
#define FMC_WRONG_SECTOR_NUM       ((uint32_t)0xFFFFFFFFU)        /*!< wrong sector number*/
#define FMC_INVALID_SIZE           ((uint32_t)0xFFFFFFFFU)        /*!< invalid sector size*/
#define FMC_INVALID_ADDR           ((uint32_t)0xFFFFFFFFU)        /*!< invalid sector address*/

#define UART_FIFO_BUF_SIZE		512

/* get the sector number, size and range of the given address */
fmc_sector_info_struct fmc_sector_info_get(uint32_t addr);
/* get the sector number by sector name */
uint32_t sector_name_to_number(uint32_t sector_name);
/* erases the sector of a given sector number */
int fmc_erase_sector_by_address(uint32_t address);
/* write 32 bit length data to a given address */
void fmc_write_32bit_data(uint32_t address, uint16_t length, int32_t* data_32);
/* read 32 bit length data from a given address */
void fmc_read_32bit_data(uint32_t address, uint16_t length, int32_t* data_32);
/* write 16 bit length data to a given address */
void fmc_write_16bit_data(uint32_t address, uint16_t length, int16_t* data_16);
/* read 16 bit length data from a given address */
void fmc_read_16bit_data(uint32_t address, uint16_t length, int16_t* data_16);
/* write 8 bit length data to a given address */
void fmc_write_8bit_data(uint32_t address, uint16_t length, int8_t* data_8);
/* read 8 bit length data from a given address */
void fmc_read_8bit_data(uint32_t address, uint16_t length, int8_t* data_8);

int DRam_Write(u32 addr, u16* data, u32 len);
int DRam_Read(u32 addr, u16* data, u32 len);
void exmc_asynchronous_sram_init(void);
extern void Uart_RxInit(EUartRxPort rxPort, EUartBaudrate baudRate, EUartParitybits parityBits, EUartStopbits stopBits, EUartMode mode, EUartEnable enable);
extern void Uart_TxInit(EUartTxPort txPort, EUartBaudrate baudRate, EUartParitybits parityBits, EUartStopbits stopBits, EUartMode mode, EUartEnable enable);
extern void Uart_SendMsg(EUartTxPort txPort, USHORT start, USHORT len, UCHAR* buffer);
extern u16  Uart_RecvMsg(EUartRxPort rxPort, USHORT len, UCHAR* buffer);
extern void Uart_ClearRecvBuffer(EUartRxPort rxPort);

#endif //____BSP_FMC_H____
